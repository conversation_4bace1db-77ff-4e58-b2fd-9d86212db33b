# HFC Export Normalization Fix Summary

## Issue Description
In Step 5 (Export Results) → Individual Well Plots (Option 1, Sub-option 1), the debug information showed "HFC p95: Not set", indicating that HFC normalization was not working properly in the export functionality.

## Root Cause Analysis
The issue was identified in the Option 1 (well marker-pair selection) workflow:

1. **Option 1 Workflow Bypasses Precompute Step**: In `pages/select_area_page.py` (lines 706-707), when users select "By well markers" mode, the code sets `precomputation_complete = True` to skip the precompute_qc_page.py step entirely.

2. **Missing HFC Percentile Calculation**: The `pages/precompute_qc_page.py` is where HFC and Spectral Decrease percentile values are normally calculated and stored in session state. Since Option 1 skips this step, these values were never calculated.

3. **Export Functionality Expects Percentile Values**: The export functionality relies on these percentile values being available in session state for proper normalization.

## Solution Implemented

### 1. Enhanced Debug Information in Export Page
**File**: `pages/export_results_page.py` (lines 725-747)

- **Before**: Direct check of `plot_settings.get('hfc_p95', 'Not set')` which showed "Not set"
- **After**: Uses robust helper functions `get_robust_hfc_normalization_value()` and `get_robust_spec_decrease_normalization_value()` for debug display
- **Improvement**: Shows actual percentile number (e.g., "HFC p90" instead of hardcoded "HFC p95") and normalization source

```python
# Get robust HFC normalization value for debug display
hfc_p95_debug, hfc_source_debug = get_robust_hfc_normalization_value(plot_settings=plot_settings)
hfc_percentile = plot_settings.get('hfc_percentile', 95.0)
st.write(f"**HFC p{int(hfc_percentile)}:** {hfc_p95_debug:.4f} (source: {hfc_source_debug})")
```

### 2. Added HFC Percentile Calculation to Option 1 Workflow
**File**: `pages/select_area_page.py` (two locations)

#### Location 1: Individual Plots Path (lines 727-784)
- Added percentile calculation when "Individual Plots" is selected
- Processes loaded trace data to calculate HFC and Spectral Decrease percentiles
- Stores values in `st.session_state.plot_settings` for use in analysis and export

#### Location 2: General Well Marker Path (lines 980-1054)  
- Added percentile calculation for general well marker selection
- Loads a sample of traces (limited to 10 for performance) for percentile calculation
- Uses default precomputation parameters for Option 1 workflow

### 3. Robust Percentile Calculation Process
Both locations implement the same robust process:

1. **Load Trace Samples**: Load actual seismic trace data from selected well marker locations
2. **Run Precomputation**: Apply default processing parameters to extract spectral descriptors
3. **Calculate Percentiles**: Compute HFC and Spectral Decrease percentile values from the processed data
4. **Store in Session State**: Save values in `st.session_state.plot_settings` for use throughout the workflow
5. **Error Handling**: Graceful fallback if percentile calculation fails

```python
# Calculate HFC and Spectral Decrease percentile values
hfc_values = []
spec_decrease_values = []
for desc in processed_traces:
    if hasattr(desc, 'get') and desc.get('hfc') is not None:
        hfc_values.extend(desc['hfc'])
    if hasattr(desc, 'get') and desc.get('spec_decrease') is not None:
        spec_decrease_values.extend(desc['spec_decrease'])

if hfc_values:
    hfc_percentile = st.session_state.plot_settings.get('hfc_percentile', 95.0)
    hfc_p95 = np.percentile(hfc_values, hfc_percentile)
    st.session_state.plot_settings['hfc_p95'] = float(hfc_p95)
    logging.info(f"Option 1: Calculated HFC p{hfc_percentile}: {hfc_p95}")
```

## Key Improvements

### ✅ **Fixed "HFC p95: Not set" Issue**
- Export debug information now shows actual calculated values instead of "Not set"
- Proper normalization values are available for export functionality

### ✅ **Dynamic Percentile Display**
- Debug information shows the actual configured percentile (e.g., "HFC p90", "HFC p95", "HFC p99")
- No longer hardcoded to "HFC p95" regardless of user configuration

### ✅ **Robust Fallback Chain**
- If percentile calculation fails, the robust helper functions provide appropriate fallbacks
- Maintains application stability even when data processing encounters issues

### ✅ **Performance Optimized**
- Limits trace loading to 10 samples for percentile calculation to avoid memory issues
- Uses default precomputation parameters optimized for Option 1 workflow

### ✅ **Comprehensive Logging**
- Added detailed logging to track percentile calculation process
- Helps with debugging and monitoring normalization value sources

## Expected Outcome

After this fix:

1. **Export Debug Information**: Shows "HFC p{configured_percentile}: {calculated_value} (source: {source_description})" instead of "HFC p95: Not set"

2. **Proper Normalization**: HFC and Spectral Decrease plots in export show realistic normalized ranges (typically [0, 2])

3. **Consistent Workflow**: Option 1 workflow now has the same robust normalization as other analysis modes

4. **Better User Experience**: Users can see exactly which normalization values are being used and where they come from

## Files Modified

1. **`pages/export_results_page.py`**: Enhanced debug information display (lines 725-747)
2. **`pages/select_area_page.py`**: Added percentile calculation to Option 1 workflow (lines 727-784 and 980-1054)
3. **`test_export_hfc_fix.py`**: Created comprehensive test script to verify the fix

## Testing

The fix has been tested with:
- ✅ Robust HFC normalization function in export context
- ✅ Debug information format with various scenarios
- ✅ Option 1 workflow simulation with percentile calculation
- ✅ Error handling and fallback scenarios

## Backward Compatibility

- ✅ All existing functionality preserved
- ✅ No breaking changes to session state structure
- ✅ Graceful handling of missing or invalid data
- ✅ Maintains compatibility with other analysis modes (Options 2-5)

This fix ensures that the Option 1 (well marker-pair selection) workflow has the same robust normalization capabilities as other analysis modes, resolving the "HFC p95: Not set" issue in export functionality.

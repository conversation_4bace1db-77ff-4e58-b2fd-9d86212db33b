# WOSS Seismic Analysis Tool: Option 1 Workflow (Well Marker Selection)

This document details the Python scripts and code flow for the WOSS Seismic Analysis Tool when using Option 1: "By well markers" for selecting data, specifically focusing on the transition from Step 3 (Select Mode/Traces) to Step 4 (View Results) and the spectral descriptor calculation process.

## Key Python Files and Their Roles

The primary Python files involved in this workflow are:

1.  **`app_ref.py`**:
    *   **Role**: The main Streamlit application file. It defines the UI, manages the application's state (using `st.session_state`), controls the flow between different steps, and orchestrates calls to other utility and processing modules.
    *   **Key Functions**: Handles UI for data loading, mode selection, parameter configuration, displaying results, and initiating calculations.

2.  **`data_utils.py`**:
    *   **Role**: Contains utility functions for data loading and preparation.
    *   **Key Functions**:
        *   `SegyHeaderLoader`: Class for loading and parsing SEG-Y file headers.
        *   `load_excel_data`: Loads well marker information from Excel files.
        *   `get_surfaces_streamlit`: Extracts unique surface names from well data for selection.
        *   `get_well_marker_pairs_streamlit`: Creates a dictionary of well-marker pairs (e.g., "WellA - SurfaceX") mapped to their row indices in the well DataFrame.
        *   `get_nearest_trace_index`: Finds the SEG-Y trace index closest to a given X, Y coordinate (from well markers).
        *   `load_trace_sample`: Loads the seismic data for a specific trace index from the SEG-Y file.

3.  **`dlogst_spec_descriptor_gpu.py`**:
    *   **Role**: Contains the core function for calculating spectral descriptors using GPU acceleration (via CuPy/PyTorch).
    *   **Key Functions**:
        *   `dlogst_spec_descriptor_gpu`: Takes a seismic trace, sampling interval, and various spectral parameters as input and returns a dictionary of calculated spectral descriptors (e.g., HFC, spectral slope, WOSS components).

4.  **`processing.py`**:
    *   **Role**: Contains functions for data processing tasks, including WOSS calculation and statistics.
    *   **Key Functions**:
        *   `calculate_stats_and_defaults`: Calculates statistical properties from a sample of the SEG-Y data to provide default parameters.
        *   `calculate_woss`: Calculates the WOSS attribute from its constituent spectral descriptors.

5.  **`visualization.py`**:
    *   **Role**: Handles the creation of plots and visualizations.
    *   **Key Functions**:
        *   `plot_spectral_descriptors`: Generates plots for individual traces, showing the input signal and selected spectral descriptors.
        *   `plot_multi_trace_section`: (Used for other modes) Generates plots for sections of traces.

## Workflow: Step 3 (Select Mode) to Step 4 (View Results) - Option 1

The workflow for analyzing data based on well marker selection involves several stages primarily managed within `app_ref.py`.

### 1. Step 3: Select Analysis Mode (`app_ref.py`)

*   The application is at `st.session_state.current_step == "select_mode"`.
*   The user selects `"By well markers"` from the "Select Mode" dropdown:
    ```python
    # In app_ref.py, within the "select_mode" step:
    st.session_state.selection_mode = st.sidebar.selectbox(
        "Select Mode",
        options=[
            "By well markers",
            # ... other options ...
        ],
        # ...
    )
    ```
*   If well data (`st.session_state.well_df`) is loaded, UI elements appear to select specific well markers (surfaces) and plot modes.
    *   `get_surfaces_streamlit(st.session_state.well_df)` from `data_utils.py` is used to populate the marker selection multiselect.
*   The user clicks the "Proceed" button, which changes the application state:
    ```python
    # In app_ref.py, at the end of the "select_mode" step:
    if st.button("Proceed", key="proceed_button"):
        st.session_state.current_step = "select_traces"
        st.rerun()
    ```

### 2. Step 3.5: Select Traces (`app_ref.py`)

The application is now at `st.session_state.current_step == "select_traces"`.

*   **Well-Marker Pair Selection**:
    *   The code block `if st.session_state.selection_mode == "By well markers":` is executed.
    *   `get_well_marker_pairs_streamlit(st.session_state.well_df)` (from `data_utils.py`) is called to get a list of available "Well - Surface" pairs.
    *   A multiselect widget allows the user to choose specific well-marker pairs for analysis. The indices of these selected pairs in the `well_df` are stored in `st.session_state.selected_indices`.

*   **Trace Data Loading**:
    *   When the user has selected pairs, the code iterates through these selections to load the corresponding seismic trace data.
    *   For each selected well-marker pair:
        1.  The X and Y coordinates are retrieved from `st.session_state.well_df`.
        2.  `get_nearest_trace_index(st.session_state.header_loader, well_x, well_y)` (from `data_utils.py`) is called. This function uses the pre-loaded SEG-Y header information (`st.session_state.header_loader`) to find the trace index in the SEG-Y file whose coordinates are closest to the well marker's coordinates.
        3.  `load_trace_sample(st.session_state.header_loader.source_file_path, trace_idx)` (from `data_utils.py`) is called. This function reads the actual seismic amplitude values for the identified `trace_idx` from the (potentially temporary) SEG-Y file.
        4.  The loaded trace sample, its index, and the well marker name are stored in a dictionary, which is then appended to `st.session_state.loaded_trace_data`.

    ```python
    # In app_ref.py, within the "select_traces" step, under "By well markers":
    # ... (selection of pairs) ...
    if selected_pairs:
        # ...
        with st.spinner("Loading trace data for selected well markers..."):
            try:
                loaded_trace_data = []
                for idx in selected_indices: # selected_indices are row numbers from well_df
                    row = st.session_state.well_df.iloc[idx]
                    well_x, well_y = row["X"], row["Y"]
                    # Find the closest trace in SEGY to this well marker
                    trace_idx = get_nearest_trace_index(st.session_state.header_loader, well_x, well_y)
                    # Load the actual trace data
                    trace_sample = load_trace_sample(st.session_state.header_loader.source_file_path, trace_idx)
                    trace_data = {
                        'trace_sample': trace_sample,
                        'trace_idx': trace_idx,
                        'well_marker_name': f"{row['Well']} - {row['Surface']}",
                        # ... other info ...
                    }
                    loaded_trace_data.append(trace_data)
                st.session_state.loaded_trace_data = loaded_trace_data
    ```

### 3. Spectral Descriptor Calculation (`app_ref.py` within "select_traces")

*   After traces are loaded, the "Calculate Descriptors" button becomes active.
*   When clicked, the application iterates through each trace in `st.session_state.loaded_trace_data`.
*   For each trace:
    1.  Spectral parameters (shape, kmax, b1, b2, etc.) are retrieved from `st.session_state.plot_settings`. These were configured in Step 2.
    2.  The `dlogst_spec_descriptor_gpu` function (from `dlogst_spec_descriptor_gpu.py`) is called.
        *   **Inputs**: The `trace_sample` (numpy array of amplitudes), `st.session_state.dt` (sampling interval), and the collected `descriptor_settings`.
        *   **Process**: This function performs the DLOGST (Double Logarithm Short-Time) transform and calculates various spectral attributes (HFC, spectral slope, bandwidth, rolloff, components for WOSS, etc.) on the GPU for efficiency.
        *   **Output**: A dictionary where keys are descriptor names (e.g., `'hfc'`, `'spec_slope'`) and values are numpy arrays or scalars representing the calculated attributes for that trace.
    3.  The returned dictionary of descriptors is appended to `st.session_state.calculated_descriptors`.
    4.  `st.session_state.analysis_complete` is set to `True`.

    ```python
    # In app_ref.py, within the "select_traces" step, under "By well markers":
    if st.button("Calculate Descriptors", ...):
        with st.spinner("Calculating spectral descriptors..."):
            calculated_descriptors = []
            for trace_data in st.session_state.loaded_trace_data:
                descriptor_settings = { # Assembled from st.session_state.plot_settings
                    'use_band_limited': st.session_state.plot_settings.get('use_band_limited', False),
                    'shape': st.session_state.plot_settings.get('shape', 0.35),
                    # ... other parameters ...
                    'epsilon': st.session_state.plot_settings.get('epsilon', 1e-4)
                }
                try:
                    if GPU_AVAILABLE:
                        descriptor = dlogst_spec_descriptor_gpu(
                            trace_data['trace_sample'],
                            st.session_state.dt,
                            **descriptor_settings # Unpacked spectral parameters
                        )
                    else:
                        # Fallback or error for CPU
                        descriptor = {}
                    calculated_descriptors.append(descriptor)
                except Exception as e:
                    st.error(f"Error calculating descriptor for {trace_data['well_marker_name']}: {e}")
                    calculated_descriptors.append({})
            st.session_state.calculated_descriptors = calculated_descriptors
            st.session_state.analysis_complete = True
    ```

*   **Statistics Calculation**:
    *   After all descriptors are computed, the code iterates through `st.session_state.calculated_descriptors` to gather values for each type of descriptor across all analyzed traces.
    *   It then calculates summary statistics (min, max, P5, P90 percentile) for each descriptor.
    *   These statistics are stored in `st.session_state.descriptor_statistics` and displayed in an expander.
    *   The `calculate_woss` function (from `processing.py`) might be implicitly called if "WOSS" is requested and its components (`hfc`, `norm_fdom`, `mag_voice_slope`) are present in the `descriptor` dictionary.

### 4. Transition to Step 4: View Results (`app_ref.py`)

*   Once `st.session_state.analysis_complete` is `True`, the "View Results" button appears.
*   Clicking this button changes the application state:
    ```python
    # In app_ref.py, at the end of the "select_traces" step (well marker part):
    if st.session_state.analysis_complete:
        # ...
        if st.button("View Results", key="view_results_button", ...):
            st.session_state.current_step = "view_results"
            st.rerun()
    ```
*   The application now renders the content defined under `elif st.session_state.current_step == "view_results":`.
*   **Displaying Plots**:
    *   The user can select which calculated descriptors to display using a multiselect widget.
    *   The `plot_spectral_descriptors` function (from `visualization.py`) is called.
    *   **Inputs**: It receives the `st.session_state.loaded_trace_data` (for original signals and trace names), `st.session_state.calculated_descriptors`, the list of `selected_outputs` (descriptors to plot), `st.session_state.dt`, and various plot settings from `st.session_state.plot_settings` (like time/frequency limits, colormaps).
    *   **Output**: A Plotly figure object, which is then rendered in the Streamlit app using `st.plotly_chart()`. This typically shows each selected trace with its input signal and the chosen spectral attributes plotted alongside.

## Data Flow Summary

1.  **Initial Data (Step 1)**:
    *   SEG-Y file -> `st.session_state.segy_file_info` -> (after loading headers) `st.session_state.header_loader`, `st.session_state.dt`, `st.session_state.segy_temp_file_path`.
    *   Well Excel file -> `st.session_state.well_file_info` -> (after loading) `st.session_state.well_df`.

2.  **Parameter Configuration (Step 2)**:
    *   User inputs for spectral parameters, plot limits, colormaps -> `st.session_state.plot_settings`.
    *   Statistics from sample traces -> `st.session_state.stats_defaults`.

3.  **Mode and Trace Selection (Step 3 & 3.5 - Option 1)**:
    *   User selects "By well markers" -> `st.session_state.selection_mode`.
    *   User selects specific well-marker pairs -> `st.session_state.selected_indices` (indices in `well_df`).
    *   `well_df` + `header_loader` + `selected_indices` -> `get_nearest_trace_index` -> `load_trace_sample` -> `st.session_state.loaded_trace_data` (list of dicts, each with 'trace_sample', 'trace_idx', 'well_marker_name').

4.  **Descriptor Calculation (Step 3.5)**:
    *   `st.session_state.loaded_trace_data` + `st.session_state.dt` + `st.session_state.plot_settings` (spectral params) -> `dlogst_spec_descriptor_gpu` -> `st.session_state.calculated_descriptors` (list of dicts, each containing calculated attributes for a trace).
    *   `st.session_state.calculated_descriptors` -> (statistics aggregation) -> `st.session_state.descriptor_statistics`.

5.  **Viewing Results (Step 4)**:
    *   `st.session_state.loaded_trace_data` + `st.session_state.calculated_descriptors` + `st.session_state.selected_outputs` (user choice) + `st.session_state.plot_settings` (display limits, colormaps) + `st.session_state.dt` -> `plot_spectral_descriptors` -> Plotly Figure displayed in UI.

This flow ensures that data is progressively loaded, processed, and stored in the session state, allowing different parts of the application to access and utilize it as needed for analysis and visualization.

#!/usr/bin/env python3
"""
Test script to verify comprehensive normalization implementation for both HFC and Spectral Decrease descriptors.

This script tests:
1. HFC normalization using get_robust_hfc_normalization_value()
2. Spectral Decrease normalization using get_robust_spec_decrease_normalization_value()
3. Integration with WOSS calculation
4. Fallback behavior when no data is available
5. Priority order of normalization value sources

Usage:
    python test_normalization_fix.py
"""

import numpy as np
import logging
import sys
import os

# Add the project root to the path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def test_hfc_normalization():
    """Test HFC normalization function with various scenarios."""
    print("\n" + "="*60)
    print("TESTING HFC NORMALIZATION")
    print("="*60)
    
    from utils.processing import get_robust_hfc_normalization_value
    
    # Test 1: With valid HFC data
    print("\nTest 1: Valid HFC data")
    hfc_data = np.random.exponential(scale=2.0, size=1000)  # Realistic HFC distribution
    hfc_p95, source = get_robust_hfc_normalization_value(hfc_data=hfc_data)
    expected_p95 = np.percentile(hfc_data, 95.0)
    print(f"  HFC P95: {hfc_p95:.4f} (expected: {expected_p95:.4f})")
    print(f"  Source: {source}")
    assert abs(hfc_p95 - expected_p95) < 1e-6, "HFC P95 calculation mismatch"
    
    # Test 2: With plot_settings containing hfc_p95
    print("\nTest 2: Plot settings with hfc_p95")
    plot_settings = {'hfc_p95': 5.5}
    hfc_p95, source = get_robust_hfc_normalization_value(plot_settings=plot_settings)
    print(f"  HFC P95: {hfc_p95:.4f} (expected: 5.5)")
    print(f"  Source: {source}")
    assert hfc_p95 == 5.5, "Should use plot_settings hfc_p95"
    
    # Test 3: No data available (fallback)
    print("\nTest 3: No data available (fallback)")
    hfc_p95, source = get_robust_hfc_normalization_value()
    print(f"  HFC P95: {hfc_p95:.4f} (expected: 1.0)")
    print(f"  Source: {source}")
    assert hfc_p95 == 1.0, "Should fallback to 1.0"
    
    print("✅ All HFC normalization tests passed!")

def test_spec_decrease_normalization():
    """Test Spectral Decrease normalization function with various scenarios."""
    print("\n" + "="*60)
    print("TESTING SPECTRAL DECREASE NORMALIZATION")
    print("="*60)
    
    from utils.processing import get_robust_spec_decrease_normalization_value
    
    # Test 1: With valid Spectral Decrease data
    print("\nTest 1: Valid Spectral Decrease data")
    spec_decrease_data = np.random.exponential(scale=1.5, size=1000)  # Realistic distribution
    spec_decrease_p95, source = get_robust_spec_decrease_normalization_value(spec_decrease_data=spec_decrease_data)
    expected_p95 = np.percentile(spec_decrease_data, 95.0)
    print(f"  Spectral Decrease P95: {spec_decrease_p95:.4f} (expected: {expected_p95:.4f})")
    print(f"  Source: {source}")
    assert abs(spec_decrease_p95 - expected_p95) < 1e-6, "Spectral Decrease P95 calculation mismatch"
    
    # Test 2: With plot_settings containing spec_decrease_p95
    print("\nTest 2: Plot settings with spec_decrease_p95")
    plot_settings = {'spec_decrease_p95': 3.2}
    spec_decrease_p95, source = get_robust_spec_decrease_normalization_value(plot_settings=plot_settings)
    print(f"  Spectral Decrease P95: {spec_decrease_p95:.4f} (expected: 3.2)")
    print(f"  Source: {source}")
    assert spec_decrease_p95 == 3.2, "Should use plot_settings spec_decrease_p95"
    
    # Test 3: No data available (fallback)
    print("\nTest 3: No data available (fallback)")
    spec_decrease_p95, source = get_robust_spec_decrease_normalization_value()
    print(f"  Spectral Decrease P95: {spec_decrease_p95:.4f} (expected: 1.0)")
    print(f"  Source: {source}")
    assert spec_decrease_p95 == 1.0, "Should fallback to 1.0"
    
    print("✅ All Spectral Decrease normalization tests passed!")

def test_woss_calculation():
    """Test WOSS calculation with robust HFC normalization."""
    print("\n" + "="*60)
    print("TESTING WOSS CALCULATION WITH ROBUST HFC NORMALIZATION")
    print("="*60)
    
    from utils.processing import calculate_woss
    
    # Create realistic test data
    num_samples = 100
    hfc = np.random.exponential(scale=2.0, size=num_samples)
    norm_fdom = np.random.uniform(0.1, 0.9, size=num_samples)
    mag_voice_slope = np.random.normal(0, 1, size=num_samples)
    
    descriptor = {
        'hfc': hfc,
        'norm_fdom': norm_fdom,
        'mag_voice_slope': mag_voice_slope
    }
    
    # Test with plot_settings containing hfc_p95
    plot_settings = {
        'hfc_p95': 4.0,
        'epsilon': 1e-4,
        'fdom_exponent': 2.0
    }
    
    print("\nTest: WOSS calculation with robust HFC normalization")
    woss = calculate_woss(descriptor, plot_settings)
    print(f"  WOSS shape: {woss.shape}")
    print(f"  WOSS range: [{np.min(woss):.4f}, {np.max(woss):.4f}]")
    print(f"  WOSS mean: {np.mean(woss):.4f}")
    print(f"  WOSS std: {np.std(woss):.4f}")
    
    # Verify WOSS is finite and reasonable
    assert np.all(np.isfinite(woss)), "WOSS should be finite"
    assert woss.shape == hfc.shape, "WOSS should have same shape as input"
    
    print("✅ WOSS calculation test passed!")

def test_integration():
    """Test integration of normalization functions in a realistic workflow."""
    print("\n" + "="*60)
    print("TESTING INTEGRATION WORKFLOW")
    print("="*60)
    
    from utils.processing import get_robust_hfc_normalization_value, get_robust_spec_decrease_normalization_value
    
    # Simulate session state with calculated percentiles
    class MockSessionState:
        def __init__(self):
            self.plot_settings = {
                'hfc_p95': 3.5,
                'spec_decrease_p95': 2.8,
                'hfc_percentile': 95.0,
                'spec_decrease_percentile': 95.0
            }
    
    mock_session = MockSessionState()
    
    print("\nTest: Integration with session state")
    
    # Test HFC normalization with session state
    hfc_p95, hfc_source = get_robust_hfc_normalization_value(session_state=mock_session)
    print(f"  HFC P95: {hfc_p95:.4f} (source: {hfc_source})")
    assert hfc_p95 == 3.5, "Should use session state hfc_p95"
    
    # Test Spectral Decrease normalization with session state
    spec_decrease_p95, spec_decrease_source = get_robust_spec_decrease_normalization_value(session_state=mock_session)
    print(f"  Spectral Decrease P95: {spec_decrease_p95:.4f} (source: {spec_decrease_source})")
    assert spec_decrease_p95 == 2.8, "Should use session state spec_decrease_p95"
    
    print("✅ Integration test passed!")

def main():
    """Run all tests."""
    print("Starting comprehensive normalization tests...")
    
    try:
        test_hfc_normalization()
        test_spec_decrease_normalization()
        test_woss_calculation()
        test_integration()
        
        print("\n" + "="*60)
        print("🎉 ALL TESTS PASSED! 🎉")
        print("="*60)
        print("\nComprehensive normalization implementation is working correctly!")
        print("✅ HFC normalization: PASS")
        print("✅ Spectral Decrease normalization: PASS")
        print("✅ WOSS calculation: PASS")
        print("✅ Integration workflow: PASS")
        
    except Exception as e:
        print(f"\n❌ TEST FAILED: {e}")
        logging.error(f"Test failed: {e}", exc_info=True)
        sys.exit(1)

if __name__ == "__main__":
    main()

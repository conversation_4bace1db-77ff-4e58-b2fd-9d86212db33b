#!/usr/bin/env python3
"""
Comprehensive debug script to trace descriptor data types through the WOSS pipeline.
This script identifies where numeric spectral descriptors are being converted to strings.
"""

import streamlit as st
import numpy as np
import logging
import json
from typing import Dict, List, Any, Union
import pickle
import sys
import traceback

def analyze_descriptor_data_types(descriptor: Any, name: str = "descriptor") -> Dict[str, Any]:
    """Analyze a single descriptor's data types in detail."""
    analysis = {
        'name': name,
        'type': type(descriptor).__name__,
        'is_dict': isinstance(descriptor, dict),
        'is_string': isinstance(descriptor, str),
        'keys': [],
        'key_types': {},
        'numpy_arrays': {},
        'string_values': {},
        'errors': []
    }
    
    try:
        if isinstance(descriptor, dict):
            analysis['keys'] = list(descriptor.keys())
            
            for key, value in descriptor.items():
                value_type = type(value).__name__
                analysis['key_types'][key] = value_type
                
                if isinstance(value, np.ndarray):
                    analysis['numpy_arrays'][key] = {
                        'dtype': str(value.dtype),
                        'shape': value.shape,
                        'size': value.size,
                        'sample_value': str(value.flat[0]) if value.size > 0 else 'empty'
                    }
                elif isinstance(value, str):
                    analysis['string_values'][key] = value[:100] + ('...' if len(value) > 100 else '')
                elif isinstance(value, (int, float, np.number)):
                    analysis['key_types'][key] += f" (value: {value})"
                    
        elif isinstance(descriptor, str):
            analysis['string_length'] = len(descriptor)
            analysis['string_preview'] = descriptor[:200] + ('...' if len(descriptor) > 200 else '')
            
            # Check if it might be a serialized dictionary
            if descriptor.strip().startswith('{') and descriptor.strip().endswith('}'):
                analysis['might_be_serialized_dict'] = True
                try:
                    import ast
                    parsed = ast.literal_eval(descriptor)
                    analysis['serialized_dict_keys'] = list(parsed.keys()) if isinstance(parsed, dict) else None
                except:
                    analysis['serialized_dict_parse_error'] = True
                    
    except Exception as e:
        analysis['errors'].append(f"Analysis error: {str(e)}")
        
    return analysis

def trace_session_state_descriptors():
    """Trace all descriptor-related data in session state."""
    print("=== SESSION STATE DESCRIPTOR TRACE ===")
    
    descriptor_keys = [
        'calculated_descriptors',
        'individual_well_analysis_results', 
        'loaded_trace_data',
        'precomputed_data_output'
    ]
    
    for key in descriptor_keys:
        if key in st.session_state:
            data = st.session_state[key]
            print(f"\n{key}: {type(data).__name__} with {len(data) if hasattr(data, '__len__') else 'N/A'} items")
            
            if isinstance(data, list) and data:
                # Analyze first few items
                for i, item in enumerate(data[:3]):
                    analysis = analyze_descriptor_data_types(item, f"{key}[{i}]")
                    print(f"  Item {i}: {analysis['type']}")
                    
                    if analysis['is_dict'] and analysis['keys']:
                        print(f"    Keys: {analysis['keys']}")
                        if analysis['numpy_arrays']:
                            print(f"    NumPy arrays: {list(analysis['numpy_arrays'].keys())}")
                        if analysis['string_values']:
                            print(f"    String values: {list(analysis['string_values'].keys())}")
                    elif analysis['is_string']:
                        print(f"    String length: {analysis.get('string_length', 'unknown')}")
                        if analysis.get('might_be_serialized_dict'):
                            print(f"    Might be serialized dict with keys: {analysis.get('serialized_dict_keys')}")
        else:
            print(f"\n{key}: NOT FOUND in session state")

def test_gpu_function_output():
    """Test GPU function output to ensure it returns proper data types."""
    print("\n=== GPU FUNCTION OUTPUT TEST ===")
    
    try:
        # Check if GPU functions are available
        gpu_available = st.session_state.get('GPU_AVAILABLE', False)
        print(f"GPU Available: {gpu_available}")
        
        if gpu_available:
            from utils.dlogst_spec_descriptor_gpu import dlogst_spec_descriptor_gpu_2d_chunked
            
            # Create test data
            test_data = np.random.randn(2, 100).astype(np.float32)
            dt = 0.004
            
            print("Testing GPU function with synthetic data...")
            result = dlogst_spec_descriptor_gpu_2d_chunked(
                test_data, dt, batch_size=2
            )
            
            print(f"GPU function result type: {type(result)}")
            if isinstance(result, dict):
                print(f"Result keys: {list(result.keys())}")
                for key, value in result.items():
                    print(f"  {key}: {type(value)} - {value.shape if isinstance(value, np.ndarray) else 'not array'}")
                    if isinstance(value, np.ndarray):
                        print(f"    dtype: {value.dtype}, sample: {value.flat[0] if value.size > 0 else 'empty'}")
            else:
                print(f"ERROR: GPU function returned {type(result)}, expected dict")
                
    except Exception as e:
        print(f"GPU function test failed: {e}")
        traceback.print_exc()

def test_session_state_serialization():
    """Test how session state handles numpy arrays."""
    print("\n=== SESSION STATE SERIALIZATION TEST ===")
    
    # Create test descriptor
    test_descriptor = {
        'data': np.random.randn(100).astype(np.float32),
        'peak_freq': np.array([50.0], dtype=np.float32),
        'hfc': np.random.randn(100).astype(np.float32),
        'test_string': 'this_is_a_string'
    }
    
    print("Original test descriptor:")
    analysis = analyze_descriptor_data_types(test_descriptor, "test_descriptor")
    print(f"  Type: {analysis['type']}")
    print(f"  Keys: {analysis['keys']}")
    print(f"  NumPy arrays: {list(analysis['numpy_arrays'].keys())}")
    
    # Store in session state
    st.session_state.test_descriptor = test_descriptor
    
    # Retrieve from session state
    retrieved = st.session_state.test_descriptor
    
    print("\nRetrieved from session state:")
    analysis = analyze_descriptor_data_types(retrieved, "retrieved_descriptor")
    print(f"  Type: {analysis['type']}")
    print(f"  Keys: {analysis['keys']}")
    print(f"  NumPy arrays: {list(analysis['numpy_arrays'].keys())}")
    print(f"  String values: {list(analysis['string_values'].keys())}")
    
    # Check if types changed
    if analysis['numpy_arrays'] != analyze_descriptor_data_types(test_descriptor)['numpy_arrays']:
        print("  WARNING: NumPy array types changed during session state storage!")
    
    # Clean up
    if 'test_descriptor' in st.session_state:
        del st.session_state.test_descriptor

def check_validation_logic():
    """Check the validation logic in export_results_page.py."""
    print("\n=== VALIDATION LOGIC CHECK ===")
    
    # Test the validation logic with different descriptor types
    test_cases = [
        {'data': np.array([1, 2, 3]), 'peak_freq': np.array([50.0])},  # Valid dict
        "{'data': [1, 2, 3], 'peak_freq': [50.0]}",  # String representation
        {'error': 'Test error'},  # Error dict
        {},  # Empty dict
        None,  # None value
        "random string",  # Random string
    ]
    
    for i, test_case in enumerate(test_cases):
        print(f"\nTest case {i}: {type(test_case).__name__}")
        
        # Simulate validation logic
        if isinstance(test_case, dict):
            if test_case and 'error' not in test_case:
                has_spectral_data = any(key in test_case for key in ['data', 'peak_freq', 'hfc'])
                print(f"  Valid dict with spectral data: {has_spectral_data}")
            elif 'error' in test_case:
                print(f"  Error dict: {test_case.get('error')}")
            else:
                print("  Empty dict")
        elif isinstance(test_case, str):
            print(f"  String: {test_case[:50]}...")
            # Test recovery mechanism
            if test_case.strip().startswith('{') and test_case.strip().endswith('}'):
                try:
                    import ast
                    recovered = ast.literal_eval(test_case)
                    print(f"  Recovery successful: {type(recovered)}")
                except:
                    print("  Recovery failed")
        else:
            print(f"  Other type: {test_case}")

def main():
    """Main debug function."""
    print("WOSS Descriptor Pipeline Debug Script")
    print("=" * 60)
    
    # Initialize session state if needed
    if 'current_step' not in st.session_state:
        try:
            from common.session_state import initialize_session_state
            initialize_session_state()
            print("Initialized session state")
        except:
            print("Could not initialize session state")
    
    trace_session_state_descriptors()
    test_gpu_function_output()
    test_session_state_serialization()
    check_validation_logic()
    
    print("\n=== SUMMARY AND RECOMMENDATIONS ===")
    
    # Check current state
    calculated_descriptors = st.session_state.get('calculated_descriptors', [])
    if calculated_descriptors:
        first_desc = calculated_descriptors[0]
        analysis = analyze_descriptor_data_types(first_desc, "first_calculated_descriptor")
        
        print(f"First calculated descriptor analysis:")
        print(f"  Type: {analysis['type']}")
        print(f"  Is string: {analysis['is_string']}")
        print(f"  Is dict: {analysis['is_dict']}")
        
        if analysis['is_string'] and analysis.get('might_be_serialized_dict'):
            print("  ISSUE IDENTIFIED: Descriptors are stored as serialized strings!")
            print("  RECOMMENDATION: Implement proper numpy array handling in session state")
        elif analysis['is_dict'] and not analysis['numpy_arrays']:
            print("  ISSUE IDENTIFIED: Dictionary descriptors lack numpy arrays!")
            print("  RECOMMENDATION: Check GPU function output and data conversion")
        elif analysis['is_dict'] and analysis['numpy_arrays']:
            print("  GOOD: Descriptors appear to be proper dictionaries with numpy arrays")
    else:
        print("No calculated descriptors found in session state")

if __name__ == "__main__":
    main()

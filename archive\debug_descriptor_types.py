#!/usr/bin/env python3
"""
Debug script to analyze descriptor data types in WOSS Seismic Analysis Tool.
This script helps identify where numeric spectral descriptors are being converted to strings.
"""

import streamlit as st
import numpy as np
import logging
from typing import Dict, List, Any

def analyze_session_state_descriptors():
    """Analyze descriptors in session state to identify data type issues."""
    print("=== DESCRIPTOR TYPE ANALYSIS ===")
    
    # Check individual_well_analysis_results
    individual_results = st.session_state.get('individual_well_analysis_results', [])
    print(f"Individual analysis results: {len(individual_results)} items")
    
    if individual_results:
        print("\n--- Individual Analysis Results Analysis ---")
        for i, result in enumerate(individual_results[:3]):  # Analyze first 3
            print(f"\nResult {i}:")
            print(f"  Type: {type(result)}")
            if isinstance(result, dict):
                print(f"  Keys: {list(result.keys())}")
                
                if 'descriptors' in result:
                    desc = result['descriptors']
                    print(f"  Descriptors type: {type(desc)}")
                    
                    if isinstance(desc, dict):
                        print(f"  Descriptor keys: {list(desc.keys())}")
                        
                        # Check a few key descriptors
                        key_descriptors = ['data', 'peak_freq', 'hfc', 'norm_fdom']
                        for key in key_descriptors:
                            if key in desc:
                                value = desc[key]
                                print(f"    {key}: {type(value)} - {value.shape if isinstance(value, np.ndarray) else 'not array'}")
                                if isinstance(value, np.ndarray):
                                    print(f"      dtype: {value.dtype}, sample: {value.flat[0] if value.size > 0 else 'empty'}")
                    else:
                        print(f"  Descriptors is not a dict: {desc}")
                
                if 'error' in result:
                    print(f"  Error: {result['error']}")
    
    # Check calculated_descriptors
    calculated_descriptors = st.session_state.get('calculated_descriptors', [])
    print(f"\nCalculated descriptors: {len(calculated_descriptors)} items")
    
    if calculated_descriptors:
        print("\n--- Calculated Descriptors Analysis ---")
        for i, desc in enumerate(calculated_descriptors[:3]):  # Analyze first 3
            print(f"\nDescriptor {i}:")
            print(f"  Type: {type(desc)}")
            
            if isinstance(desc, dict):
                print(f"  Keys: {list(desc.keys())}")
                
                if 'error' in desc:
                    print(f"  Error: {desc['error']}")
                else:
                    # Check key spectral descriptors
                    key_descriptors = ['data', 'peak_freq', 'hfc', 'norm_fdom']
                    for key in key_descriptors:
                        if key in desc:
                            value = desc[key]
                            print(f"    {key}: {type(value)} - {value.shape if isinstance(value, np.ndarray) else 'not array'}")
                            if isinstance(value, np.ndarray):
                                print(f"      dtype: {value.dtype}, sample: {value.flat[0] if value.size > 0 else 'empty'}")
                            elif isinstance(value, str):
                                print(f"      STRING VALUE: {value[:100]}...")
            elif isinstance(desc, str):
                print(f"  STRING DESCRIPTOR: {desc[:100]}...")
            else:
                print(f"  UNEXPECTED TYPE: {desc}")

def validate_descriptor_structure(descriptor: Dict[str, Any]) -> Dict[str, Any]:
    """Validate and report on descriptor structure."""
    validation_report = {
        'is_valid_dict': isinstance(descriptor, dict),
        'has_error': False,
        'missing_keys': [],
        'invalid_types': [],
        'string_values': [],
        'valid_numeric_keys': []
    }
    
    if not validation_report['is_valid_dict']:
        return validation_report
    
    if 'error' in descriptor:
        validation_report['has_error'] = True
        return validation_report
    
    # Expected spectral descriptor keys and their expected types
    expected_keys = {
        'data': np.ndarray,
        'peak_freq': (np.ndarray, int, float, np.number),
        'spec_centroid': (np.ndarray, int, float, np.number),
        'fdom': (np.ndarray, int, float, np.number),
        'norm_fdom': (np.ndarray, int, float, np.number),
        'hfc': (np.ndarray, int, float, np.number),
        'spec_slope': (np.ndarray, int, float, np.number),
        'mag_voice_slope': (np.ndarray, int, float, np.number)
    }
    
    for key, expected_type in expected_keys.items():
        if key not in descriptor:
            validation_report['missing_keys'].append(key)
        else:
            value = descriptor[key]
            if isinstance(value, expected_type):
                validation_report['valid_numeric_keys'].append(key)
            elif isinstance(value, str):
                validation_report['string_values'].append(f"{key}:{value[:50]}...")
            else:
                validation_report['invalid_types'].append(f"{key}:{type(value).__name__}")
    
    return validation_report

def main():
    """Main debug function."""
    if not hasattr(st, 'session_state'):
        print("Streamlit session state not available. Run this within a Streamlit app.")
        return
    
    print("WOSS Seismic Analysis Tool - Descriptor Type Debug")
    print("=" * 50)
    
    analyze_session_state_descriptors()
    
    # Additional validation
    calculated_descriptors = st.session_state.get('calculated_descriptors', [])
    if calculated_descriptors:
        print("\n=== DETAILED VALIDATION ===")
        for i, desc in enumerate(calculated_descriptors[:2]):  # Validate first 2
            print(f"\nValidating descriptor {i}:")
            report = validate_descriptor_structure(desc)
            
            for key, value in report.items():
                if value:  # Only print non-empty values
                    print(f"  {key}: {value}")

if __name__ == "__main__":
    main()

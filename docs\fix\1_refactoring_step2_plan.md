# Refactoring Plan: Step 2 Statistics Calculation

## 1. Problem Description

The user reported that the statistics calculated in Step 2 ("Configure Analysis Parameters") of the WOSS Streamlit application are not representative of the actual seismic data. This occurs after clicking the "Calculate Defaults & Configure Display" button, when the "Statistic" tab appears.

## 2. Investigation Summary

*   **Initial Check (`app.py`):** Confirmed that Step 2 UI is rendered by `pages/configure_display_page.py`.
*   **UI Logic (`pages/configure_display_page.py`):**
    *   Statistics calculation is triggered by the "Calculate Defaults & Configure Display" button.
    *   The button calls `utils.processing.calculate_stats_and_defaults`.
    *   Tabs ("General", "Spectral Descriptors", "Statistic") appear only if the calculation is successful and populates `st.session_state.stats_defaults`.
    *   The "Statistic" tab displays the raw calculated statistics.
    *   The "Spectral Descriptors" tab displays sliders for setting plot limits, using P05/P95 from the stats as defaults.
*   **Calculation Logic (`utils/processing.py`):**
    *   `calculate_stats_and_defaults` samples traces, stacks them, and calls `process_trace_array`.
    *   `process_trace_array` calls `utils.dlogst_spec_descriptor_gpu.dlogst_spec_descriptor_gpu` for each trace in the sample.
    *   **Issue 1:** `process_trace_array` *also* calls `calculate_woss` within its loop. This `calculate_woss` uses `hfc_p95` from the input `plot_settings`. During initial stats calculation, this `hfc_p95` is not yet statistically derived from the sample, causing WOSS to be calculated inconsistently using per-trace normalization.
    *   `calculate_stats_and_defaults` then calls `_compute_descriptor_statistics` to aggregate results. This function uses `clean_array` (removes NaN/Inf, clamps outliers) before calculating final statistics (Min, Max, Mean, Percentiles).
*   **Core Descriptor Logic (`utils/dlogst_spec_descriptor_gpu.py`):**
    *   **Issue 2:** The function `dlogst_spec_descriptor_gpu` implements standard FFT-based spectral attributes (HFC, Dominant Frequency, etc.), **not** the specific DLOGST/WOSS decomposition logic.
    *   **Issue 3:** User-configured parameters from Step 2 (`shape`, `kmax`, `int_val`, etc.) are **ignored** by this core function.
    *   **Issue 4:** `mag_voice_slope` is calculated incorrectly as a duplicate of dominant frequency (`inst_freq`).

## 3. Root Causes

1.  The core spectral descriptor function (`dlogst_spec_descriptor_gpu`) does not implement the required DLOGST/WOSS mathematics.
2.  User-configured spectral parameters are not used in the core calculation.
3.  `mag_voice_slope` calculation is fundamentally incorrect.
4.  WOSS calculation during the statistics phase uses inconsistent, per-trace normalization instead of a statistically derived factor.

## 4. Proposed Solution

A two-part approach is required:

**Part 1: Correct Core Descriptor Logic (`utils/dlogst_spec_descriptor_gpu.py`)**

1.  **Rewrite/Replace `dlogst_spec_descriptor_gpu`:** Modify this function (or create a new one) to correctly implement the DLOGST/WOSS spectral decomposition based on its mathematical definition.
2.  **Incorporate User Parameters:** Ensure the revised function utilizes the user-provided parameters (`shape`, `kmax`, `int_val`, `b1`, `b2`, etc.) passed via `plot_settings`.
3.  **Fix `mag_voice_slope`:** Implement the correct mathematical formula for `mag_voice_slope` within the DLOGST/WOSS context.

**Part 2: Refactor Statistics Calculation Flow (`utils/processing.py`)**

1.  **Modify `calculate_stats_and_defaults` and helpers:**
    *   Call the **revised core function** (from Part 1) within `process_trace_array` to get the *corrected* primary DLOGST/WOSS descriptors (HFC, FDom, corrected Slope, etc.) for the sampled traces. **Do not** calculate WOSS at this stage.
    *   Call `_compute_descriptor_statistics` to calculate statistics for these primary descriptors.
    *   Extract the statistically derived `hfc_p95` (or other necessary normalization factors) from these results.
    *   **After** primary stats are computed, iterate through the stored primary descriptor results for the sample traces.
    *   Calculate WOSS for each trace using the statistically derived `hfc_p95` and the corrected slope value.
    *   Compute statistics specifically for the consistently calculated WOSS values.
    *   Combine all statistics (primary descriptors + WOSS) and default plot ranges (`defaults`) into the final result dictionary.

## 5. Expected Outcome

Implementing this plan will ensure:
*   The correct DLOGST/WOSS spectral decomposition is performed.
*   User-configured parameters directly influence the descriptor calculations.
*   All descriptor components, including `mag_voice_slope` and `WOSS`, are calculated correctly and consistently.
*   The statistics displayed in the "Statistic" tab accurately represent the characteristics of the seismic data based on the chosen parameters.

## 6. Flow Diagram (Proposed)

```mermaid
graph TD
    A[Start calculate_stats_and_defaults] --> B{Sample Traces};
    B --> C{Stack Traces};
    C --> D{Calculate **Corrected DLOGST/WOSS** Primary Descriptors (HFC, FDom, Corrected Slope etc.) for each trace in sample using **Revised Core Function** and **User Parameters**};
    D --> E{Compute Stats for Primary Descriptors (incl. hfc_p95)};
    E --> F{Extract hfc_p95};
    F --> G{Calculate WOSS for each trace in sample using extracted hfc_p95 and Corrected Slope};
    G --> H{Compute Stats for WOSS};
    E --> I{Format Stats & Defaults for Primary Descriptors};
    H --> J{Format Stats & Defaults for WOSS};
    I --> K{Combine Results};
    J --> K;
    K --> L[Return Correct & Representative Stats & Defaults];

    style D fill:#ccffcc,stroke:#333,stroke-width:2px
    style G fill:#ccffcc,stroke:#333,stroke-width:2px
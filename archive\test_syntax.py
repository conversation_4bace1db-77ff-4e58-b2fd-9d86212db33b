#!/usr/bin/env python3
import ast
import sys

def check_syntax(filename):
    try:
        with open(filename, 'r', encoding='utf-8') as f:
            source = f.read()
        ast.parse(source)
        print(f"✅ {filename} has valid Python syntax")
        return True
    except SyntaxError as e:
        print(f"❌ Syntax error in {filename}:")
        print(f"  Line {e.lineno}: {e.text.strip() if e.text else 'N/A'}")
        print(f"  Error: {e.msg}")
        return False
    except Exception as e:
        print(f"❌ Error checking {filename}: {e}")
        return False

if __name__ == "__main__":
    check_syntax("pages/analyze_data_page.py")
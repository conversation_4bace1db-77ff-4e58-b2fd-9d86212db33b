# WOSS Seismic Analysis Tool - Application Structure Analysis

## 1. Main Components and Workflow

The WOSS Seismic Analysis Tool is a Streamlit application for analyzing seismic data with a focus on spectral analysis. The application follows a multi-step workflow:

1. **Data Loading**: Users upload SEG-Y files and optional well data in Excel format
2. **Configuration**: Users configure display parameters and spectral analysis settings
3. **Mode Selection**: Users select an analysis mode (well markers, inline/crossline, AOI, polyline)
4. **Trace Selection**: Based on the selected mode, users choose specific traces for analysis
5. **Analysis**: The application calculates spectral descriptors for the selected traces
6. **Visualization**: Results are displayed through interactive plots
7. **Export**: For AOI mode, users can export results to SEG-Y files

The application uses GPU acceleration for spectral descriptor calculations when available, with fallback to CPU processing.

## 2. Dependencies Between Files

The application consists of several Python modules with the following dependencies:

- **app.py**: Main Streamlit application that imports and uses all other modules
  - Imports from data_utils.py for SEG-Y and well data handling
  - Imports from visualization.py for plotting functions
  - Imports from processing.py for spectral analysis calculations
  - Imports from utils.py for helper functions
  - Imports from dlogst_spec_descriptor_gpu.py for GPU-accelerated calculations
  - Imports from export_utils.py for data export functionality

- **data_utils.py**: Handles SEG-Y file loading and well data processing
  - No dependencies on other project modules
  - Uses external libraries: segyio, numpy, pandas

- **visualization.py**: Contains plotting functions for various visualizations
  - Imports from utils.py for polyline functions
  - Imports from processing.py for WOSS calculation

- **processing.py**: Contains spectral analysis and processing functions
  - Imports from data_utils.py for trace loading
  - Imports from utils.py for batch size estimation
  - Imports from dlogst_spec_descriptor_gpu.py for GPU functions

- **utils.py**: Contains utility functions used across the application
  - No dependencies on other project modules

- **export_utils.py**: Handles exporting analysis results
  - Likely depends on data_utils.py (not fully analyzed)

- **dlogst_spec_descriptor_gpu.py**: Contains GPU-accelerated spectral descriptor calculations
  - No dependencies on other project modules
  - Uses external libraries: numpy, torch/cupy

## 3. Key Functionality by Utility File

### data_utils.py
- SEG-Y file header loading and parsing
- Well data loading from Excel files
- Trace extraction and sampling
- Coordinate system handling and scaling
- Streamlit-compatible versions of GUI functions

### visualization.py
- Basemap visualization of seismic survey with well locations
- Interactive basemap for trace selection
- Spectral descriptor plotting for single traces
- Multi-trace section visualization
- Polyline selection interface

### processing.py
- Spectral descriptor calculation
- WOSS (Weighted-Optimum Spectral Shape) calculation
- Statistical analysis of trace data
- Parameter configuration and normalization
- Batch processing for large datasets

### utils.py
- Polyline parsing and trace selection
- Distance calculations for trace selection
- GPU memory estimation for batch size optimization
- Exception handling utilities

### export_utils.py
- Export of calculated attributes to SEG-Y format
- Attribute selection for export

### dlogst_spec_descriptor_gpu.py
- GPU-accelerated spectral descriptor calculations
- Batch processing for multiple traces
- Magnitude spectrogram calculation

## 4. Main Pages/Steps for Refactored Application

Based on the current application structure, the refactored application should include these main pages/steps:

1. **Data Loading Page**
   - SEG-Y file upload
   - Well data upload (optional)
   - Header byte configuration
   - Coordinate scaler settings

2. **Configuration Page**
   - Basemap visualization
   - Parameter configuration
   - Spectral analysis settings
   - Display settings (colormaps, limits)

3. **Analysis Mode Selection Page**
   - Well marker mode
   - Inline/crossline mode
   - AOI (Area of Interest) mode
   - Polyline mode

4. **Trace Selection Page** (specific to each mode)
   - Well marker selection interface
   - Inline/crossline selection interface
   - AOI definition interface
   - Polyline definition interface

5. **Results Visualization Page**
   - Single trace analysis view
   - Multi-trace comparative view
   - Section view for inline/crossline/polyline
   - Output selection controls

6. **Export Configuration Page** (for AOI mode)
   - Attribute selection
   - Export grouping options
   - Batch size configuration

## 5. Challenges and Considerations for Refactoring

1. **GPU Dependency Management**
   - The application relies on GPU acceleration for performance
   - Need to maintain fallback to CPU processing
   - Consider containerization for consistent GPU environment

2. **Memory Management**
   - Large SEG-Y files can cause memory issues
   - Current implementation uses batch processing
   - Refactored app should maintain or improve memory efficiency

3. **UI/UX Improvements**
   - Current app has a complex multi-step workflow
   - Consider wizard-style interface for better user guidance
   - Improve feedback during long-running operations

4. **Code Modularity**
   - Current implementation has some tight coupling between modules
   - Refactor to improve separation of concerns
   - Consider using class-based components for better encapsulation

5. **Error Handling**
   - Improve error messages and recovery options
   - Add validation for user inputs
   - Implement better logging for debugging

6. **Performance Optimization**
   - Optimize batch sizes for different operations
   - Consider caching intermediate results
   - Improve rendering performance for large plots

7. **Streamlit Limitations**
   - Work within Streamlit's stateless execution model
   - Manage session state effectively
   - Consider alternatives for performance-critical components

By addressing these challenges and following the structure outlined above, the refactored application should maintain all existing functionality while improving usability, maintainability, and performance.

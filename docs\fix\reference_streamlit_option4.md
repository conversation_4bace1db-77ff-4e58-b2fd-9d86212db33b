# WOSS Seismic Analysis Tool: Option 4 Workflow (By inline/crossline section - AOI Export)

This document details the Python scripts and code flow for the WOSS Seismic Analysis Tool when using Option 4: "By inline/crossline section (AOI)" for selecting data, with a focus on configuring and performing data export.

## Key Python Files and Their Roles

The primary Python files involved in this workflow include:

1.  **`app_ref.py`**:
    *   **Role**: The main Streamlit application file. It defines the UI, manages application state (`st.session_state`), controls the flow between steps, and orchestrates calls to other modules for AOI definition, export configuration, and initiating the export process.
    *   **Key Functions**: Handles UI for AOI bounds input, attribute selection for export, grouping/batching options, and managing the export and download stages.

2.  **`data_utils.py`**:
    *   **Role**: Contains utility functions for data loading, preparation, and SEGY file manipulation.
    *   **Key Functions**:
        *   `SegyHeaderLoader`: Essential for accessing trace headers (inline, crossline, coordinates, original trace indices) to define the AOI and prepare for export.
        *   `load_trace_sample`: Loads seismic amplitude data for individual trace indices within the AOI.
        *   `merge_segy_batch_files`: (Potentially used) Could be involved in creating or combining SEG-Y files during the export process, especially if attributes are exported into separate files or batched. *Note: The direct use for writing new SEGY files with attributes would typically involve `segyio` functionalities for creating new files, copying headers, and writing new trace data.*

3.  **`dlogst_spec_descriptor_gpu.py`**:
    *   **Role**: Contains core functions for calculating spectral descriptors using GPU acceleration.
    *   **Key Functions**:
        *   `dlogst_spec_descriptor_gpu_2d_chunked`: Crucial for efficiently processing all traces within the defined AOI in batches to calculate the selected spectral attributes for export.

4.  **`processing.py`**:
    *   **Role**: Contains functions for data processing tasks, including WOSS calculation.
    *   **Key Functions**:
        *   `calculate_woss`: Calculates the WOSS attribute from its constituent spectral descriptors if WOSS is selected for export.

5.  **`utils.py`**:
    *   **Role**: Contains general utility functions.
    *   **Key Functions**:
        *   `get_suggested_batch_size_for_export`: Suggests a batch size for export based on the number of unique groups (inlines/crosslines).
        *   `get_suggested_batch_size`: Suggests GPU processing batch size.

## Workflow: Step 3 (Select Mode) to Data Export - Option 4

The workflow for defining an Area of Interest (AOI) and exporting calculated attributes involves several stages managed within `app_ref.py`.

### 1. Step 3: Select Analysis Mode (`app_ref.py`)

*   The application is at `st.session_state.current_step == "select_mode"`.
*   The user selects `"By inline/crossline section (AOI)"` from the "Select Mode" dropdown.
    ```python
    # In app_ref.py, within the "select_mode" step:
    st.session_state.selection_mode = st.sidebar.selectbox(
        "Select Mode",
        options=[
            # ... other options ...
            "By inline/crossline section (AOI)",
            # ... other options ...
        ],
        # ...
    )
    ```
*   **AOI Bounds Input**:
    *   Number input fields appear for "Inline Min", "Inline Max", "Crossline Min", and "Crossline Max". These values are stored in `st.session_state` (e.g., `st.session_state.aoi_inline_min`).
    *   The processing option is fixed to "Full AOI".
*   The user clicks the "Proceed" button:
    ```python
    # In app_ref.py, at the end of the "select_mode" step:
    if st.button("Proceed", key="proceed_button"):
        st.session_state.current_step = "select_traces" # This step handles AOI trace identification
        st.rerun()
    ```

### 2. Step 3.5: Select Traces (Identify Traces within AOI) (`app_ref.py`)

The application is at `st.session_state.current_step == "select_traces"`.

*   **Filter Traces by AOI**:
    *   The code block `elif st.session_state.selection_mode == "By inline/crossline section (AOI)":` is executed.
    *   A Pandas DataFrame (`headers_df`) is created from `st.session_state.header_loader` attributes (inlines, crosslines, coordinates, trace_idx).
    *   This DataFrame is filtered based on the AOI bounds (`st.session_state.aoi_inline_min`, etc.) to create `aoi_df`.
    *   The `trace_idx` column from `aoi_df` is extracted and stored in `st.session_state.selected_indices`.
    ```python
    # In app_ref.py, within "select_traces" for "By inline/crossline section (AOI)":
    headers_df = pd.DataFrame({
        'inline': st.session_state.header_loader.inlines,
        'crossline': st.session_state.header_loader.crosslines,
        # ... x, y ...
    })
    headers_df['trace_idx'] = st.session_state.header_loader.unique_indices

    aoi_df = headers_df[
        (headers_df['inline'] >= st.session_state.aoi_inline_min) &
        (headers_df['inline'] <= st.session_state.aoi_inline_max) &
        (headers_df['crossline'] >= st.session_state.aoi_xline_min) &
        (headers_df['crossline'] <= st.session_state.aoi_xline_max)
    ]
    st.session_state.selected_indices = aoi_df['trace_idx'].tolist()
    ```
*   **Transition to Export Configuration**:
    *   A "Configure Export" button appears. Clicking it transitions the application:
    ```python
    # In app_ref.py, within "select_traces" for AOI:
    if st.button("Configure Export", key="configure_export_button"):
        st.session_state.current_step = "configure_export"
        st.rerun()
    ```

### 3. Step 3.6: Configure AOI Export (`app_ref.py`)

The application is at `st.session_state.current_step == "configure_export"`.

*   **Attribute Selection**:
    *   A multiselect widget allows the user to choose which attributes to export from `EXPORTABLE_ATTR_DISPLAY_NAMES`.
    *   The selected display names are mapped to their internal names (e.g., "WOSS" -> "WOSS", "Spectral Slope" -> "spec_slope") using `ATTR_NAME_MAP` and stored.
*   **Grouping and Batching**:
    *   User selects `grouping_type` ("inline" or "crossline") for organizing output files.
    *   User inputs `batch_step` (number of inlines/crosslines per output file).
    *   `get_suggested_batch_size_for_export` (from `utils.py`) provides a default for `batch_step`.
*   **GPU Processing Batch Size**:
    *   If `GPU_AVAILABLE`, a number input for `st.session_state.batch_size` (traces per GPU chunk during calculation) is shown.
*   **Initiate Export**:
    *   The "Start Export Process" button is enabled if GPU is available and attributes are selected.
    *   Clicking this button is intended to trigger the actual data processing and file generation. *The provided `app_ref.py` does not fully implement the next steps but sets the stage.*
    ```python
    # In app_ref.py, within "configure_export" step:
    if st.button("Start Export Process", key="start_export_button", disabled=disable_export_button):
        if not selected_attrs_internal:
            st.error("Please select at least one attribute to export.")
        elif not GPU_AVAILABLE:
            st.error("GPU is required for AOI export processing and is not available.")
        else:
            # Store selections for the export process
            st.session_state.export_attributes = selected_attrs_internal
            st.session_state.export_grouping = grouping_type
            st.session_state.export_batch_step = batch_step
            # GPU batch size is already in st.session_state.batch_size

            st.session_state.current_step = "export_process" # Transition to actual export
            st.session_state.export_in_progress = True
            # Create a unique output directory for this export session
            if st.session_state.export_output_dir is None:
                # Create a unique temporary directory for export
                # Example: tempfile.mkdtemp(prefix="woss_export_")
                # For simplicity in app_ref.py, it might be a fixed name or timestamped
                # This part is not fully detailed in the provided app_ref.py
                st.session_state.export_output_dir = tempfile.mkdtemp(prefix="woss_aoi_export_")
                logging.info(f"Created export output directory: {st.session_state.export_output_dir}")

            st.rerun()
    ```

### 4. Step: Export Process (Conceptual - `app_ref.py`, `export_process` state)

This step is largely conceptual in the provided `app_ref.py` but would involve the following logic:

*   The application is at `st.session_state.current_step == "export_process"`.
*   A status indicator (e.g., `st.spinner` or `st.progress`) would show export progress.
*   **Data Grouping**:
    *   The `aoi_df` (containing trace indices and their inline/crossline numbers) is grouped based on `st.session_state.export_grouping` (e.g., by unique inline numbers).
    *   These groups are further batched according to `st.session_state.export_batch_step` (e.g., 10 inlines per batch).
*   **Iterate Through Batches**:
    *   For each batch of inlines/crosslines:
        1.  **Identify Traces**: Collect all `trace_idx` from `st.session_state.selected_indices` that belong to the current batch.
        2.  **Load Trace Data**: Load the actual seismic data for these traces using `load_trace_sample` in a loop, then stack them into a 2D numpy array (`section_data_2d`).
        3.  **Calculate Attributes**:
            *   Retrieve spectral parameters from `st.session_state.plot_settings`.
            *   Call `dlogst_spec_descriptor_gpu_2d_chunked` with `section_data_2d`, `dt`, `st.session_state.batch_size` (GPU processing batch), and the list of `st.session_state.export_attributes` (internal names). This returns a dictionary of 2D attribute arrays.
            *   If "WOSS" is in `st.session_state.export_attributes`, call `calculate_woss` using the necessary component attributes (which should also have been calculated).
        4.  **Write SEGY Files**:
            *   For each calculated attribute in the current batch:
                *   Create a new SEGY file name (e.g., `AttributeName_InlineRange_CrosslineRange.sgy`) in `st.session_state.export_output_dir`.
                *   Use `segyio` to open the new file in write mode.
                *   Copy the file-level headers from the original SEGY file (or set appropriate new ones).
                *   For each trace in the current batch of `section_data_2d`:
                    *   Retrieve its original trace header from `st.session_state.header_loader.segyfile.header[original_trace_idx]`.
                    *   Set this header for the new trace in the output SEGY file.
                    *   Write the corresponding 1D slice from the 2D calculated attribute array as the trace data.
                *   Close the new SEGY file.
*   **Update Progress**: Update `st.progress` or log messages.
*   Once all batches are processed:
    ```python
    # Conceptual end of export_process step in app_ref.py
    # st.session_state.export_in_progress = False
    # st.session_state.exported_files_info = { 'path': st.session_state.export_output_dir, 'num_files': ... } # Store info
    # st.session_state.current_step = "download_export"
    # st.rerun()
    ```

### 5. Step: Download Export (Conceptual - `app_ref.py`, `download_export` state)

This step is also conceptual in the provided `app_ref.py`.

*   The application is at `st.session_state.current_step == "download_export"`.
*   **Zip Files**:
    *   Create a zip archive of all the SEGY files generated in `st.session_state.export_output_dir`.
*   **Provide Download Link**:
    *   Use `st.download_button` to allow the user to download the zip file.
    *   The `reset_state()` function should clean up `st.session_state.export_output_dir` after download or when starting a new analysis.

## Data Flow Summary (Option 4 - AOI Export)

1.  **Initial Data (Step 1)**:
    *   SEG-Y -> `st.session_state.header_loader`, `st.session_state.dt`, `st.session_state.segy_temp_file_path`.

2.  **Parameter Configuration (Step 2)**:
    *   User inputs for spectral parameters -> `st.session_state.plot_settings`.

3.  **Mode and AOI Definition (Step 3 & 3.5)**:
    *   User selects "AOI" mode, inputs bounds (`aoi_inline_min`, etc.).
    *   `header_loader` + AOI bounds -> filter `headers_df` -> `st.session_state.selected_indices` (trace indices in AOI).

4.  **Export Configuration (Step 3.6)**:
    *   User selections -> `st.session_state.export_attributes` (internal names), `st.session_state.export_grouping`, `st.session_state.export_batch_step`, `st.session_state.batch_size` (GPU processing).
    *   Output directory created -> `st.session_state.export_output_dir`.

5.  **Export Process (Conceptual - `export_process` state)**:
    *   `selected_indices` + `header_loader` -> grouped and batched trace collections.
    *   For each batch:
        *   Trace indices -> `load_trace_sample` (loop) -> `section_data_2d` (2D numpy array).
        *   `section_data_2d` + `dt` + `plot_settings` + `batch_size` (GPU) + `export_attributes` -> `dlogst_spec_descriptor_gpu_2d_chunked` -> Dictionary of 2D attribute arrays.
        *   WOSS calculated if needed via `calculate_woss`.
        *   Each 2D attribute array + original trace headers -> New SEGY files written to `export_output_dir` using `segyio`.

6.  **Download Export (Conceptual - `download_export` state)**:
    *   Files in `export_output_dir` -> Zipped archive.
    *   User downloads zip file via `st.download_button`.

This workflow facilitates bulk processing of defined AOIs and exporting multiple calculated seismic attributes into standard SEG-Y format, ready for use in other geoscience software. The efficiency relies heavily on GPU-accelerated batch processing.

#!/usr/bin/env python3
"""
Debug script to help identify issues with the "View Results" functionality.
This script can be run to check the current state of the application and identify potential issues.
"""

import streamlit as st
import logging
import sys
import os

# Add the current directory to the path so we can import modules
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def check_session_state():
    """Check the current session state for debugging."""
    print("=== SESSION STATE DEBUG ===")
    
    # Check critical session state variables
    critical_vars = [
        'current_step',
        'selection_mode', 
        'plot_mode_wells',
        'well_analysis_sub_option',
        'analysis_complete',
        'loaded_trace_data',
        'calculated_descriptors',
        'individual_well_analysis_results',
        'plot_settings',
        'dt',
        'GPU_AVAILABLE'
    ]
    
    for var in critical_vars:
        value = st.session_state.get(var, 'NOT SET')
        if isinstance(value, list):
            print(f"{var}: {len(value)} items")
        elif isinstance(value, dict):
            print(f"{var}: {len(value)} keys")
        else:
            print(f"{var}: {value}")
    
    print("\n=== DETAILED ANALYSIS ===")
    
    # Check loaded trace data
    loaded_trace_data = st.session_state.get('loaded_trace_data', [])
    if loaded_trace_data:
        print(f"Loaded trace data: {len(loaded_trace_data)} items")
        for i, trace in enumerate(loaded_trace_data[:3]):  # Show first 3
            print(f"  Trace {i}: {list(trace.keys())}")
    else:
        print("No loaded trace data found")
    
    # Check calculated descriptors
    calculated_descriptors = st.session_state.get('calculated_descriptors', [])
    if calculated_descriptors:
        print(f"Calculated descriptors: {len(calculated_descriptors)} items")
        for i, desc in enumerate(calculated_descriptors[:3]):  # Show first 3
            if desc:
                print(f"  Descriptor {i}: {list(desc.keys())}")
                if 'error' in desc:
                    print(f"    ERROR: {desc['error']}")
            else:
                print(f"  Descriptor {i}: EMPTY")
    else:
        print("No calculated descriptors found")
    
    # Check individual analysis results
    individual_results = st.session_state.get('individual_well_analysis_results', [])
    if individual_results:
        print(f"Individual analysis results: {len(individual_results)} items")
        for i, result in enumerate(individual_results[:3]):  # Show first 3
            print(f"  Result {i}: {list(result.keys())}")
            if 'error' in result:
                print(f"    ERROR: {result['error']}")
    else:
        print("No individual analysis results found")

def check_gpu_availability():
    """Check GPU availability and functions."""
    print("\n=== GPU AVAILABILITY CHECK ===")
    
    try:
        import torch
        print(f"PyTorch available: {torch.__version__}")
        print(f"CUDA available: {torch.cuda.is_available()}")
        if torch.cuda.is_available():
            print(f"CUDA device count: {torch.cuda.device_count()}")
            print(f"Current device: {torch.cuda.current_device()}")
            print(f"Device name: {torch.cuda.get_device_name()}")
    except ImportError:
        print("PyTorch not available")
    
    try:
        from utils.dlogst_spec_descriptor_gpu import (
            dlogst_spec_descriptor_gpu,
            dlogst_spec_descriptor_gpu_2d_chunked,
            dlogst_spec_descriptor_gpu_2d_chunked_mag
        )
        print("GPU functions imported successfully")
    except ImportError as e:
        print(f"GPU functions import failed: {e}")

def check_visualization_functions():
    """Check if visualization functions are available."""
    print("\n=== VISUALIZATION FUNCTIONS CHECK ===")
    
    try:
        from utils.visualization import plot_spectral_descriptors
        print("plot_spectral_descriptors imported successfully")
    except ImportError as e:
        print(f"plot_spectral_descriptors import failed: {e}")
    
    try:
        import plotly.graph_objects as go
        print("Plotly available")
    except ImportError as e:
        print(f"Plotly import failed: {e}")

def main():
    """Main debug function."""
    print("WOSS Application Debug Script")
    print("=" * 50)
    
    # Initialize Streamlit session state if not already done
    if 'current_step' not in st.session_state:
        from common.session_state import initialize_session_state
        initialize_session_state()
        print("Initialized session state")
    
    check_session_state()
    check_gpu_availability()
    check_visualization_functions()
    
    print("\n=== RECOMMENDATIONS ===")
    
    # Provide recommendations based on findings
    if not st.session_state.get('analysis_complete', False):
        print("- Analysis is not marked as complete. Run the analysis step first.")
    
    if not st.session_state.get('calculated_descriptors', []):
        print("- No calculated descriptors found. Check if analysis completed successfully.")
    
    if not st.session_state.get('loaded_trace_data', []):
        print("- No loaded trace data found. This may cause visualization issues.")
    
    if st.session_state.get('well_analysis_sub_option') == "Plot Individual Wells Analysis":
        individual_results = st.session_state.get('individual_well_analysis_results', [])
        if not individual_results:
            print("- Individual analysis mode selected but no individual results found.")
        else:
            error_count = len([r for r in individual_results if 'error' in r])
            if error_count > 0:
                print(f"- {error_count} individual analysis results have errors.")

if __name__ == "__main__":
    main()

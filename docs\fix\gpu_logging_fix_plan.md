# Plan to Resolve Repetitive GPU Logging Issue

## Problem Description

An endless stream of log messages: `'YYYY-MM-DD HH:MM:SS,ms - INFO - Successfully imported GPU spectral descriptor functions'` occurs during certain user interactions in the Streamlit application (specifically, option 1, sub-option 1, and viewing results).

## Root Cause Analysis

The investigation revealed the following:
1.  The log message "Successfully imported GPU spectral descriptor functions" originates from [`app.py`](app.py) at line 32.
2.  This logging occurs within a `try-except` block that handles the import of GPU-specific functions (`utils.dlogst_spec_descriptor_gpu`).
3.  This import block is executed at the top level of [`app.py`](app.py).
4.  Streamlit applications re-run the entire script from top to bottom on most user interactions. Navigational changes, such as selecting options that change `st.session_state.current_step` and trigger `st.rerun()`, cause this top-level import and logging to execute repeatedly.

## Proposed Solution

To prevent the repeated execution of the GPU import logic and its associated logging, we will use Streamlit's `@st.cache_resource` decorator. This decorator is designed for operations like loading models or initializing resources that should only happen once per session and persist across script re-runs.

**Steps:**

1.  **Define a Cached Initialization Function in `app.py`**:
    *   Create a new function, e.g., `initialize_gpu_functions()`.
    *   Decorate this function with `@st.cache_resource`.
    *   Move the entire `try-except` block responsible for importing `utils.dlogst_spec_descriptor_gpu` (currently lines 25-41 in [`app.py`](app.py)) into this new function.
    *   The function will perform the import, log the success/failure message *once* (when the cached function runs for the first time), and return the `GPU_AVAILABLE` status.
    *   Modify the log message slightly to indicate it's from the cached function, e.g., "Successfully imported GPU spectral descriptor functions (cached)".

2.  **Call the Cached Function in `app.py`**:
    *   Replace the existing top-level import block with a single call to `GPU_AVAILABLE = initialize_gpu_functions()`.

**Illustrative Code Change in `app.py`:**

```python
import streamlit as st
import logging
import torch
# ... other imports ...
from common.constants import APP_TITLE # Assuming APP_TITLE is defined here

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

# Configure page - THIS MUST BE THE FIRST STREAMLIT COMMAND
st.set_page_config(page_title=APP_TITLE, layout="wide")

@st.cache_resource
def initialize_gpu_functions():
    """
    Tries to import GPU-specific functions and returns their availability.
    Logs the import status once.
    """
    gpu_available_status = False
    # Ensure necessary modules are available in this scope if they were global before
    # For dlogst_spec_descriptor_gpu functions, they are imported directly.
    try:
        from utils.dlogst_spec_descriptor_gpu import (
            dlogst_spec_descriptor_gpu,
            dlogst_spec_descriptor_gpu_2d_chunked,
            dlogst_spec_descriptor_gpu_2d_chunked_mag
        )
        # These functions are now in the local scope of initialize_gpu_functions.
        # If they need to be globally accessible as before, this approach needs adjustment,
        # or the calling code needs to be aware they are namespaced under utils.
        # Given the original code, they were imported into the app.py global scope.
        # A common pattern with @st.cache_resource for functions is to return them.
        # However, for this specific case, the original code implies they become globally available.
        # Let's stick to the original pattern of making them available if import succeeds.
        # The simplest way is to re-import them globally if needed after this function confirms availability,
        # or ensure the utils module itself handles this.
        # For now, the key is that GPU_AVAILABLE is set correctly and logging is once.
        # The original code made them global by direct import.
        # If we want to keep them global:
        # global dlogst_spec_descriptor_gpu, dlogst_spec_descriptor_gpu_2d_chunked, dlogst_spec_descriptor_gpu_2d_chunked_mag
        # from utils.dlogst_spec_descriptor_gpu import ... (this makes them global in this function's scope)
        # To make them truly global to app.py, they'd need to be assigned to globals() or returned and assigned.
        # The original code:
        # from utils.dlogst_spec_descriptor_gpu import (...)
        # GPU_AVAILABLE = True
        # This makes them available in app.py's global scope.
        # So, the import inside the cached function will also make them available in app.py's global scope
        # because of Python's import mechanism.

        gpu_available_status = True
        logging.info("Successfully imported GPU spectral descriptor functions (cached)")
    except ImportError as e:
        logging.warning(f"Could not import GPU functions: {e}")
        # Define dummy functions globally if GPU is not available
        # This matches the original behavior.
        _make_dummy_gpu_functions_global()
    return gpu_available_status

def _make_dummy_gpu_functions_global():
    """Helper to define dummy GPU functions in the global scope of app.py."""
    global dlogst_spec_descriptor_gpu, dlogst_spec_descriptor_gpu_2d_chunked, dlogst_spec_descriptor_gpu_2d_chunked_mag
    def dlogst_spec_descriptor_gpu(*args, **kwargs):
        raise NotImplementedError("GPU function dlogst_spec_descriptor_gpu not available.")
    def dlogst_spec_descriptor_gpu_2d_chunked(*args, **kwargs):
        raise NotImplementedError("GPU function dlogst_spec_descriptor_gpu_2d_chunked not available.")
    def dlogst_spec_descriptor_gpu_2d_chunked_mag(*args, **kwargs):
        raise NotImplementedError("GPU function dlogst_spec_descriptor_gpu_2d_chunked_mag not available.")

# --- Initialize GPU Functions ---
# Define placeholders for linters before the cached function potentially redefines them.
# This is important if other top-level code might try to reference them before the cached call.
# However, given the structure, it's better to ensure initialize_gpu_functions is called early.
dlogst_spec_descriptor_gpu = None
dlogst_spec_descriptor_gpu_2d_chunked = None
dlogst_spec_descriptor_gpu_2d_chunked_mag = None

GPU_AVAILABLE = initialize_gpu_functions()

# If GPU is not available, the dummy functions would have been made global by _make_dummy_gpu_functions_global.
# If GPU is available, the real functions are in the global scope due to the import within initialize_gpu_functions.

# ... (rest of app.py, including page imports, session state initialization, etc.) ...

# Example:
# from pages.analyze_data_page import render as render_analyze_data
# Now, render_analyze_data can safely assume that if GPU_AVAILABLE is True,
# the functions like dlogst_spec_descriptor_gpu are available globally.
```

**Note on Global Functions:** The original code imports `dlogst_spec_descriptor_gpu` (and others) directly into the global namespace of `app.py`. The proposed cached function `initialize_gpu_functions` will also achieve this when the `from utils.dlogst_spec_descriptor_gpu import ...` line is executed within it, due to how Python imports work. If the import fails, the `_make_dummy_gpu_functions_global` helper ensures placeholder functions are globally available, maintaining the original script's behavior regarding the availability of these function names.

## Mermaid Diagram

```mermaid
graph TD
    A[User Interaction in Streamlit App] --> B{App Script Re-run};
    B -- Current Behavior --> C[Top-level GPU Import in app.py];
    C --> D["logging.info(...) executed REPEATEDLY"];
    D --> A; subgraph Problem Loop
    direction LR
    A
    B
    C
    D
    end

    P[User Interaction in Streamlit App] --> Q{App Script Re-run};
    Q -- Proposed Solution --> R[Call @st.cache_resource def initialize_gpu_functions()];
    R -- First Run Only --> S[GPU Import Logic within initialize_gpu_functions];
    S --> T["logging.info(...) executed ONCE"];
    R -- Subsequent Runs --> U[Return cached GPU_AVAILABLE status];
    T --> V[GPU_AVAILABLE set, functions globally available];
    U --> V;
    V --> W[Rest of App Logic Uses GPU_AVAILABLE and global GPU functions];
    W -.-> P; subgraph Solution Flow
    direction LR
    P
    Q
    R
    S
    T
    U
    V
    W
    end
```

## Expected Outcome

The GPU functions will be imported (or fallbacks defined) only once when the Streamlit application session starts. The "Successfully imported GPU spectral descriptor functions" log message will appear only once in the console/logs per session, resolving the endless stream of messages. The `GPU_AVAILABLE` flag will be correctly set and available throughout the application.
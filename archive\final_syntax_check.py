#!/usr/bin/env python3

import ast
import sys
import traceback

def check_syntax(filename):
    """Check syntax of a Python file."""
    try:
        with open(filename, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Try to parse the file
        ast.parse(content)
        print(f"✅ {filename}: Syntax is valid")
        return True
        
    except SyntaxError as e:
        print(f"❌ {filename}: Syntax error at line {e.lineno}: {e.msg}")
        if e.text:
            print(f"   Text: {e.text.strip()}")
        return False
        
    except Exception as e:
        print(f"❌ {filename}: Error: {e}")
        traceback.print_exc()
        return False

def main():
    """Main function to check syntax of all relevant files."""
    files_to_check = [
        "pages/analyze_data_page.py",
        "check_syntax.py",
        "test_syntax.py"
    ]
    
    print("🔍 Checking syntax of Python files...")
    print("=" * 50)
    
    all_valid = True
    for filename in files_to_check:
        try:
            if not check_syntax(filename):
                all_valid = False
        except FileNotFoundError:
            print(f"⚠️  {filename}: File not found")
            all_valid = False
    
    print("=" * 50)
    if all_valid:
        print("🎉 All files have valid syntax!")
        return 0
    else:
        print("💥 Some files have syntax errors!")
        return 1

if __name__ == "__main__":
    sys.exit(main())
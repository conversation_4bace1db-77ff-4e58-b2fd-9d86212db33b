#!/usr/bin/env python3
"""
Test script to verify the descriptor data type fixes in WOSS Seismic Analysis Tool.
This script tests the GPU functions, session state handling, and validation logic.
"""

import numpy as np
import logging
import sys
import os

# Add the project root to the path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def test_gpu_function_output():
    """Test that GPU functions return proper numpy arrays."""
    print("=== TESTING GPU FUNCTION OUTPUT ===")
    
    try:
        from utils.dlogst_spec_descriptor_gpu import dlogst_spec_descriptor_gpu_2d_chunked
        
        # Create test data
        test_data = np.random.randn(3, 100).astype(np.float32)
        dt = 0.004
        
        print(f"Input data shape: {test_data.shape}, dtype: {test_data.dtype}")
        
        # Test GPU function
        result = dlogst_spec_descriptor_gpu_2d_chunked(
            test_data, dt, batch_size=2
        )
        
        print(f"GPU function returned type: {type(result)}")
        
        if isinstance(result, dict):
            print(f"Result keys: {list(result.keys())}")
            
            # Check each key
            all_valid = True
            for key, value in result.items():
                if isinstance(value, np.ndarray):
                    print(f"  {key}: numpy array, shape={value.shape}, dtype={value.dtype}")
                    if value.dtype.kind not in ['f', 'i', 'u']:
                        print(f"    WARNING: {key} has non-numeric dtype: {value.dtype}")
                        all_valid = False
                elif isinstance(value, str):
                    print(f"  {key}: STRING VALUE: {value[:50]}...")
                    all_valid = False
                else:
                    print(f"  {key}: {type(value)} - {value}")
            
            if all_valid:
                print("✓ GPU function output validation PASSED")
                return True
            else:
                print("✗ GPU function output validation FAILED")
                return False
        else:
            print(f"✗ GPU function returned {type(result)}, expected dict")
            return False
            
    except Exception as e:
        print(f"✗ GPU function test failed: {e}")
        return False

def test_session_state_handler():
    """Test the session state handler for numpy array preservation."""
    print("\n=== TESTING SESSION STATE HANDLER ===")
    
    try:
        # Mock streamlit session state
        class MockSessionState:
            def __init__(self):
                self.data = {}
            
            def __setitem__(self, key, value):
                self.data[key] = value
            
            def __getitem__(self, key):
                return self.data[key]
            
            def __contains__(self, key):
                return key in self.data
            
            def get(self, key, default=None):
                return self.data.get(key, default)
        
        # Mock streamlit
        import streamlit as st
        st.session_state = MockSessionState()
        
        from utils.session_state_handler import safe_store_descriptors, safe_retrieve_descriptors, validate_descriptor_types
        
        # Create test descriptors
        test_descriptors = [
            {
                'data': np.random.randn(100).astype(np.float32),
                'peak_freq': np.array([50.0], dtype=np.float32),
                'hfc': np.random.randn(100).astype(np.float32),
                'spec_centroid': np.array([25.0], dtype=np.float32),
                'well_marker_name': 'Test Well 1',
                'trace_idx': 0
            },
            {
                'data': np.random.randn(100).astype(np.float32),
                'peak_freq': np.array([45.0], dtype=np.float32),
                'hfc': np.random.randn(100).astype(np.float32),
                'spec_centroid': np.array([30.0], dtype=np.float32),
                'well_marker_name': 'Test Well 2',
                'trace_idx': 1
            }
        ]
        
        print(f"Created {len(test_descriptors)} test descriptors")
        
        # Test storage
        safe_store_descriptors(test_descriptors, 'test_descriptors')
        print("✓ Safe storage completed")
        
        # Test retrieval
        retrieved_descriptors = safe_retrieve_descriptors('test_descriptors')
        print(f"✓ Safe retrieval completed, got {len(retrieved_descriptors)} descriptors")
        
        # Test validation
        validation_report = validate_descriptor_types(retrieved_descriptors)
        print(f"Validation report: {validation_report}")
        
        # Check if data types are preserved
        all_valid = True
        for i, desc in enumerate(retrieved_descriptors):
            if not isinstance(desc, dict):
                print(f"✗ Descriptor {i} is not a dict: {type(desc)}")
                all_valid = False
                continue
                
            for key in ['data', 'peak_freq', 'hfc', 'spec_centroid']:
                if key in desc:
                    value = desc[key]
                    if not isinstance(value, np.ndarray):
                        print(f"✗ Descriptor {i}, key '{key}' is not numpy array: {type(value)}")
                        all_valid = False
                    elif value.dtype.kind not in ['f', 'i', 'u']:
                        print(f"✗ Descriptor {i}, key '{key}' has non-numeric dtype: {value.dtype}")
                        all_valid = False
        
        if all_valid:
            print("✓ Session state handler validation PASSED")
            return True
        else:
            print("✗ Session state handler validation FAILED")
            return False
            
    except Exception as e:
        print(f"✗ Session state handler test failed: {e}")
        return False

def test_string_recovery():
    """Test the string recovery mechanism."""
    print("\n=== TESTING STRING RECOVERY ===")
    
    try:
        from utils.session_state_handler import safe_retrieve_descriptors
        
        # Mock streamlit session state with string descriptors
        class MockSessionState:
            def __init__(self):
                self.data = {}
            
            def __setitem__(self, key, value):
                self.data[key] = value
            
            def __getitem__(self, key):
                return self.data[key]
            
            def __contains__(self, key):
                return key in self.data
            
            def get(self, key, default=None):
                return self.data.get(key, default)
        
        import streamlit as st
        st.session_state = MockSessionState()
        
        # Create test descriptors with string representations
        string_descriptors = [
            "{'data': [1.0, 2.0, 3.0], 'peak_freq': [50.0], 'hfc': [10.0, 20.0, 30.0]}",
            {'error': 'Test error message'},
            {'data': np.array([1, 2, 3]), 'peak_freq': np.array([45.0])}  # Valid descriptor
        ]
        
        st.session_state['test_string_descriptors'] = string_descriptors
        
        # Test recovery
        recovered_descriptors = safe_retrieve_descriptors('test_string_descriptors')
        
        print(f"Original: {len(string_descriptors)} descriptors")
        print(f"Recovered: {len(recovered_descriptors)} descriptors")
        
        # Check recovery results
        recovery_success = False
        for i, desc in enumerate(recovered_descriptors):
            print(f"Descriptor {i}: {type(desc)}")
            if isinstance(desc, dict) and 'data' in desc and 'error' not in desc:
                recovery_success = True
                print(f"  Successfully recovered descriptor with keys: {list(desc.keys())}")
        
        if recovery_success:
            print("✓ String recovery test PASSED")
            return True
        else:
            print("✗ String recovery test FAILED")
            return False
            
    except Exception as e:
        print(f"✗ String recovery test failed: {e}")
        return False

def test_validation_logic():
    """Test the enhanced validation logic."""
    print("\n=== TESTING VALIDATION LOGIC ===")
    
    try:
        from utils.session_state_handler import validate_descriptor_types
        
        # Create test descriptors with various issues
        test_descriptors = [
            # Valid descriptor
            {
                'data': np.array([1, 2, 3], dtype=np.float32),
                'peak_freq': np.array([50.0], dtype=np.float32),
                'hfc': np.array([10, 20, 30], dtype=np.float32)
            },
            # Descriptor with string values (the main issue we're fixing)
            {
                'data': '[1.0, 2.0, 3.0]',  # String instead of array
                'peak_freq': '[50.0]',       # String instead of array
                'hfc': '[10.0, 20.0, 30.0]' # String instead of array
            },
            # Error descriptor
            {'error': 'Test error message'},
            # String descriptor (serialized)
            "{'data': [1, 2, 3], 'peak_freq': [50.0]}",
            # Empty descriptor
            {},
            # None value
            None
        ]
        
        analysis = validate_descriptor_types(test_descriptors)
        
        print(f"Validation analysis:")
        print(f"  Total count: {analysis['total_count']}")
        print(f"  Valid dict count: {analysis['valid_dict_count']}")
        print(f"  Error dict count: {analysis['error_dict_count']}")
        print(f"  String count: {analysis['string_count']}")
        print(f"  Other type count: {analysis['other_type_count']}")
        print(f"  NumPy array keys: {analysis['numpy_array_keys']}")
        print(f"  String value keys: {analysis['string_value_keys']}")
        print(f"  Type issues: {len(analysis['type_issues'])}")
        
        # Check if string value keys are detected (this is the main issue)
        if analysis['string_value_keys']:
            print(f"✓ String value detection PASSED - found: {analysis['string_value_keys']}")
            return True
        else:
            print("✗ String value detection FAILED - no string values detected")
            return False
            
    except Exception as e:
        print(f"✗ Validation logic test failed: {e}")
        return False

def main():
    """Run all tests."""
    print("WOSS Descriptor Data Type Fix Test Suite")
    print("=" * 50)
    
    tests = [
        ("GPU Function Output", test_gpu_function_output),
        ("Session State Handler", test_session_state_handler),
        ("String Recovery", test_string_recovery),
        ("Validation Logic", test_validation_logic)
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"✗ {test_name} test crashed: {e}")
            results.append((test_name, False))
    
    print("\n" + "=" * 50)
    print("TEST RESULTS SUMMARY")
    print("=" * 50)
    
    passed = 0
    for test_name, result in results:
        status = "PASSED" if result else "FAILED"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\nOverall: {passed}/{len(results)} tests passed")
    
    if passed == len(results):
        print("🎉 All tests passed! The descriptor data type fixes are working correctly.")
    else:
        print("⚠️  Some tests failed. Please review the output above for details.")
    
    return passed == len(results)

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)

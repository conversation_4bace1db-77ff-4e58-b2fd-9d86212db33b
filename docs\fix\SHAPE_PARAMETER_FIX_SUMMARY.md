# Shape Parameter TypeError Fix Summary

## Problem Description
The WOSS Seismic Analysis Tool was encountering a TypeError when processing spectral descriptors:
```
TypeError: bad operand type for unary -: 'str'
```
This error occurred in `utils/dlogst_spec_descriptor_gpu.py` at line 278:
```python
logit = kmax / (1 + kdiv * cp.exp(-shape * freqst_gpu_const))
```

## Root Cause Analysis
The issue was caused by the `shape` parameter being passed as a string `'sine'` instead of a numeric value. This happened because:

1. In `pages/select_area_page.py` line 376, the default value for `shape` was incorrectly set to `'sine'` (string)
2. The GPU function expected a numeric value but didn't validate input types
3. Other files correctly used numeric defaults (0.35) for the same parameter

## Solution Implemented

### 1. Fixed Parameter Defaults in select_area_page.py
**Before:**
```python
spectral_params = {
    'shape': st.session_state.get('shape', 'sine'),  # ❌ String default
    'kmax': st.session_state.get('kmax', 0.5),       # ❌ Inconsistent values
    # ... other parameters with inconsistent defaults
}
```

**After:**
```python
base_plot_settings = st.session_state.get('plot_settings', {})
spectral_params = {
    'shape': base_plot_settings.get('shape', 0.35),   # ✅ Numeric default
    'kmax': base_plot_settings.get('kmax', 120.0),    # ✅ Consistent with other pages
    # ... all parameters now consistent with analyze_data_page.py
}
```

### 2. Added Robust Type Validation in GPU Functions
Enhanced all three GPU functions with comprehensive type validation:

```python
# Handle shape parameter with type validation
if shape is None: 
    shape = np.float32(0.05)
else:
    # Convert string inputs to appropriate numeric values
    if isinstance(shape, str):
        if shape.lower() in ['sine', 'gaussian']:
            shape = np.float32(0.35)  # Standard default for string inputs
        else:
            try:
                shape = np.float32(float(shape))
            except (ValueError, TypeError):
                raise ValueError(f"Invalid shape parameter: '{shape}'. Expected numeric value or 'sine'/'gaussian'.")
    else:
        shape = np.float32(shape)
```

### 3. Improved Parameter Consistency
- Changed `select_area_page.py` to use the same pattern as `analyze_data_page.py`
- Now gets parameters from `plot_settings` instead of directly from session state
- All numeric defaults now match across the application

## Files Modified

1. **pages/select_area_page.py**
   - Fixed spectral parameter defaults (lines 372-386)
   - Changed from hardcoded values to plot_settings pattern
   - Made consistent with analyze_data_page.py

2. **utils/dlogst_spec_descriptor_gpu.py**
   - Added type validation for all three functions:
     - `dlogst_spec_descriptor_gpu` (lines 66-94)
     - `dlogst_spec_descriptor_gpu_2d_chunked` (lines 248-277)
     - `dlogst_spec_descriptor_gpu_2d_chunked_mag` (lines 516-545)

## Testing Results
Created comprehensive test suite (`test_shape_parameter_fix.py`) that validates:

✅ **All test cases passed (8/8):**
- None values (uses default)
- Numeric float values
- NumPy float32 values  
- String 'sine' (converts to 0.35)
- String 'gaussian' (converts to 0.35)
- Case-insensitive strings ('SINE')
- Numeric strings ('0.5')
- Invalid strings (proper error handling)

✅ **select_area_page.py compatibility confirmed**
- Parameters work correctly with GPU function
- No more TypeError when processing spectral descriptors

## Benefits of This Fix

1. **Immediate Issue Resolution**: Fixes the TypeError that was preventing spectral descriptor calculations
2. **Robust Error Handling**: GPU functions now gracefully handle various input types
3. **Consistency**: All pages now use the same parameter patterns and defaults
4. **Backward Compatibility**: Existing numeric inputs continue to work unchanged
5. **Future-Proof**: String inputs are now properly converted or rejected with clear error messages

## Verification Steps
To verify the fix is working:

1. Run the test suite: `python test_shape_parameter_fix.py`
2. Use the WOSS tool to calculate spectral descriptors in Option 1 (select_area_page.py)
3. Confirm no TypeError occurs and calculations complete successfully

The fix maintains compatibility with existing WOSS tool functionality while ensuring robust type handling for GPU-based spectral descriptor calculations.

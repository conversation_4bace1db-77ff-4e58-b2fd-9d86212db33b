# WOSS Seismic Analysis Tool: Step 2 Workflow (Configure Display and Parameters)

This document details the Python scripts and code flow for Step 2: "Configure Analysis Parameters" in the WOSS Seismic Analysis Tool, as implemented in `app_ref.py`. This step focuses on displaying SEGY information, generating a basemap, allowing users to configure spectral parameters, calculating default values based on the data, and setting display limits for subsequent analysis.

## Key Python Files and Their Roles

1.  **`app_ref.py`**:
    *   **Role**: The main Streamlit application file. For Step 2, it handles the UI for displaying SEGY metadata, basemap controls, spectral parameter inputs, triggering default calculations, and setting up display/colormap limits. It manages the state transitions within this step and to the next.
    *   **Key Functions/Sections**: The `elif st.session_state.current_step == "configure_display":` block.

2.  **`data_utils.py`**:
    *   **Role**: Provides utility functions for data access and information extraction.
    *   **Key Functions**:
        *   `SegyHeaderLoader` (instance in `st.session_state.header_loader`): Used to get inline/crossline ranges and other header-derived information.
        *   `get_surfaces_streamlit`: Extracts unique surface names from loaded well data (`st.session_state.well_df`) for basemap layer selection.

3.  **`visualization.py`**:
    *   **Role**: Handles the creation of plots.
    *   **Key Functions**:
        *   `plot_basemap_with_wells`: Generates the basemap figure using `st.session_state.header_loader` and optionally `st.session_state.well_df` with selected surfaces.

4.  **`processing.py`**:
    *   **Role**: Contains functions for data processing tasks, including statistical calculations.
    *   **Key Functions**:
        *   `calculate_stats_and_defaults`: Calculates statistical properties (min, max, percentiles) from a sample of the SEG-Y data and suggests default values for various spectral descriptors. This is a crucial function in Step 2.

5.  **`segyio` (External Library)**:
    *   **Role**: Used directly in `app_ref.py` within this step to open the temporary SEG-Y file (`st.session_state.segy_temp_file_path`) to determine the number of samples per trace for displaying the time range.

6.  **`plotly.colors` (External Library)**:
    *   **Role**: Used to get a list of available Plotly named colorscales for the colormap selection dropdowns.

## Workflow: Step 2 - Configure Analysis Parameters

This step is initiated when `st.session_state.current_step == "configure_display"`.

### 1. Basemap & SEGY Info Expander

*   **Display SEGY Information**:
    *   If `st.session_state.header_loader` is available, the application displays key information about the loaded SEG-Y file:
        *   Sampling Interval: From `st.session_state.dt`.
        *   Number of Samples & Time Range: Determined by opening the SEG-Y file (`st.session_state.segy_temp_file_path`) with `segyio` to get trace length.
        *   Number of Traces: From `st.session_state.trace_count`.
        *   Inline/Crossline Range: Obtained by calling `st.session_state.header_loader.get_inline_crossline_range()`.
    ```python
    # In app_ref.py, within "configure_display" step:
    # ...
    # with st.expander("Basemap & SEGY Info", expanded=True):
    #     st.subheader("SEGY Information")
    #     if st.session_state.header_loader:
    #         # ... (code to get num_samples, time_range_str, inline_range_str, xline_range_str) ...
    #         st.info(f"""
    #         **SEGY File Information:**
    #         - Sampling Interval: {st.session_state.dt*1000:.2f} ms
    #         - Number of Samples: {num_samples if num_samples > 0 else 'N/A'}
    #         # ... other info ...
    #         """)
    ```

*   **Basemap Controls and Display**:
    *   A checkbox (`show_basemap_controls`) toggles the visibility of basemap generation controls.
    *   If controls are shown and well data (`st.session_state.well_df`) exists:
        *   `get_surfaces_streamlit(st.session_state.well_df)` populates a multiselect for choosing surfaces to display on the basemap (`st.session_state.selected_surfaces_for_basemap`).
        *   A slider sets the `st.session_state.basemap_detail_level` ('low', 'medium', 'high').
        *   A "Generate Basemap" button sets `st.session_state.show_basemap = True` and `st.session_state.basemap_needs_update = True`.
    *   If `st.session_state.show_basemap` is true and `st.session_state.basemap_needs_update` is true (or the figure is not yet generated):
        *   `plot_basemap_with_wells(st.session_state.header_loader, well_df_basemap, detail_level=detail_level)` is called. `well_df_basemap` is the well data filtered by selected surfaces.
        *   The returned figure is stored in `st.session_state.basemap_figure`.
    *   The basemap (`st.session_state.basemap_figure`) is displayed using `st.plotly_chart`.
    *   A "Hide Basemap" button sets `st.session_state.show_basemap = False`.

### 2. Calculate Defaults & Configure Display Expander

*   **Parameter Configuration Input**:
    *   **Statistics Settings**: Inputs for "Sample Percent for Statistics" (`sample_percent`) and "Max Traces for Statistics" (`max_traces_for_stats`).
    *   **Spectral Parameters**: Inputs for `use_band_limited`, `shape`, `kmax`, `int_val`, `b1`, `b2`, `p_bandwidth`, `roll_percent`, and `epsilon` (for WOSS). These are stored in `st.session_state.plot_settings`.
    ```python
    # In app_ref.py, within "configure_display" step:
    # with st.expander("Calculate Defaults & Configure Display", expanded=False):
    #     # ... (inputs for sample_percent, max_traces_for_stats) ...
    #     # ... (inputs for shape, kmax, etc., stored in st.session_state.plot_settings) ...
    ```

*   **Calculate Defaults Button**:
    *   When "Calculate Defaults & Configure Display" is clicked:
        1.  The path to the SEG-Y file is retrieved from `st.session_state.header_loader.source_file_path`.
        2.  The spectral parameters entered by the user are collected from `st.session_state.plot_settings`.
        3.  `calculate_stats_and_defaults` (from `processing.py`) is called.
            *   **Inputs**: SEG-Y path, `header_loader` instance, `dt`, sample percentage, max traces for stats, and the collected spectral parameters.
            *   **Process**: This function typically reads a subset of traces, calculates all spectral descriptors for these sample traces, and then computes statistics (min, max, percentiles like P5, P95) for each descriptor. It also returns suggested default values for display limits based on these statistics.
            *   **Output**: A dictionary containing `stats` (the calculated statistics for each descriptor) and `defaults` (suggested display limits).
        4.  The returned dictionary is stored in `st.session_state.stats_defaults`.
        5.  `st.session_state.plot_settings` is updated with the `defaults` part of the returned dictionary.
        6.  `st.rerun()` is called to refresh the UI and display the subsequent configuration sections based on the calculated defaults.
    ```python
    # In app_ref.py, within "configure_display" step:
    # if st.button("Calculate Defaults & Configure Display", key="calc_defaults"):
    #     # ...
    #     spectral_params = { # Assembled from st.session_state.plot_settings
    #         'use_band_limited': st.session_state.plot_settings.get('use_band_limited', False),
    #         # ... other spectral parameters ...
    #     }
    #     defaults = calculate_stats_and_defaults(
    #         segy_path,
    #         st.session_state.header_loader,
    #         st.session_state.dt,
    #         st.session_state.plot_settings.get('sample_percent', 1.0),
    #         st.session_state.plot_settings.get('max_traces_for_stats', 500),
    #         **spectral_params
    #     )
    #     if defaults:
    #         st.session_state.stats_defaults = defaults
    #         st.session_state.plot_settings.update(defaults.get('defaults', {}))
    #         st.rerun()
    ```

*   **Display Configuration (Post-Defaults Calculation)**:
    *   This section is shown only if `st.session_state.stats_defaults` is not `None`.
    *   **Tab "General"**:
        *   Inputs for "Time Min/Max (seconds)" (`time_min`, `time_max`). The max value is suggested by trace length.
        *   Inputs for "Frequency Min/Max (Hz)" for spectrograms (`freq_min`, `freq_max`). Max is Nyquist frequency.
        *   These are stored in `st.session_state.plot_settings` and also mapped to keys like `'Time (Y-axis)'` and `'Frequency'`.
    *   **Tab "Spectral Descriptors"**:
        *   This tab allows fine-tuning of display/colormap limits and colormap selection for each spectral descriptor.
        *   A predefined dictionary `descriptor_settings` provides initial defaults (min, max, colormap) for each descriptor.
        *   The statistical results from `st.session_state.stats_defaults['stats']` (e.g., P5, P95 values) are used to refine these default display limits.
        *   For each descriptor (e.g., "Input Signal", "HFC", "WOSS"):
            *   Two `st.number_input` widgets for Min and Max display/colormap limits.
            *   An `st.selectbox` for choosing a colormap from `plotly.colors.named_colorscales()`.
            *   The chosen values are stored in `st.session_state.plot_settings` using keys like `hfc_cmap_min`, `hfc_cmap_max`, `hfc_cmap_name`.
    ```python
    # In app_ref.py, within "configure_display" step, after defaults are calculated:
    # with tab_spectral:
    #     # ... (descriptor_settings and available_colormaps defined) ...
    #     # ... (stats from st.session_state.stats_defaults used to update descriptor_settings defaults) ...
    #     for idx, (descriptor, settings) in enumerate(descriptor_settings.items()):
    #         # ... (col = cols[idx % 3]) ...
    #         # ... (key_base, cmap_min_key_state, cmap_max_key_state, cmap_name_key_state defined) ...
    #         cmap_min_val = st.number_input(f"Min", value=float(current_colormap_limits[0]), ...)
    #         cmap_max_val = st.number_input(f"Max", value=float(current_colormap_limits[1]), ...)
    #         selected_colormap = st.selectbox(f"Colormap", options=available_colormaps, ...)
    #         st.session_state.plot_settings[cmap_min_key_state] = cmap_min_val
    #         st.session_state.plot_settings[cmap_max_key_state] = cmap_max_val
    #         st.session_state.plot_settings[cmap_name_key_state] = selected_colormap
    ```
    *   **Tab "Statistic"**:
        *   Displays the raw statistics (`st.session_state.stats_defaults['stats']`) in JSON format.
        *   A selectbox for "Colormap for Seismic Section Background" (`section_colormap`).

### 3. Confirmation and Transition

*   A button "Confirm Settings & Proceed" is displayed.
*   When clicked, the application transitions to the next step:
    ```python
    # In app_ref.py, at the end of the "configure_display" step:
    if st.button("Confirm Settings & Proceed", key="confirm_settings"):
        st.session_state.current_step = "select_mode" # Transition to Step 3
        logging.info("Settings confirmed, proceeding to mode selection.")
        st.rerun()
    ```

## Data Flow Summary for Step 2

1.  **Input Data (from Step 1 & Session State)**:
    *   `st.session_state.header_loader`: SEG-Y header information.
    *   `st.session_state.dt`: Sampling interval.
    *   `st.session_state.trace_count`: Total number of traces.
    *   `st.session_state.segy_temp_file_path`: Path to the temporary SEG-Y file.
    *   `st.session_state.well_df` (optional): Loaded well data.

2.  **User Inputs within Step 2**:
    *   Basemap surface selections, detail level.
    *   Statistics calculation settings (`sample_percent`, `max_traces_for_stats`).
    *   Spectral parameters (`shape`, `kmax`, etc.).
    *   Time/frequency plot limits.
    *   Descriptor-specific display/colormap limits and colormap choices.

3.  **Key Processing & Data Generation**:
    *   SEGY metadata (time range, inline/crossline ranges) extracted for display.
    *   `plot_basemap_with_wells` generates `st.session_state.basemap_figure`.
    *   `calculate_stats_and_defaults` uses SEG-Y data and spectral parameters to produce `st.session_state.stats_defaults` (containing raw statistics and suggested default display limits).

4.  **Output Data (Stored in Session State for Next Steps)**:
    *   `st.session_state.stats_defaults`: Holds the calculated statistics and initial default settings.
    *   `st.session_state.plot_settings`: Continuously updated throughout Step 2. By the end of this step, it contains:
        *   All user-defined spectral parameters.
        *   User-defined (or default-refined) time and frequency plot limits.
        *   User-defined (or default-refined) display/colormap limits for each spectral descriptor (e.g., `hfc_cmap_min`, `woss_cmap_max`).
        *   Selected colormaps for each descriptor (e.g., `hfc_cmap_name`).
        *   Selected section colormap.
    *   `st.session_state.basemap_figure` (if generated).
    *   `st.session_state.selected_surfaces_for_basemap`.

This comprehensive set of configurations in `st.session_state.plot_settings` is critical for the subsequent analysis and visualization steps, ensuring that calculations and plots adhere to the user's preferences and data-driven defaults.

# Option 4: AOI Export Implementation Documentation

This document provides detailed information about the implementation of Option 4 (By inline/crossline section - AOI Export) for the WOSS Seismic Analysis Tool.

## Overview

Option 4 allows users to select an Area of Interest (AOI) defined by inline and crossline ranges, and export calculated seismic attributes for all traces within that AOI to SEG-Y files.

## Implementation Details

### 1. Selection Mode UI (select_area_page.py)

The AOI selection UI allows users to:
- View available inline/crossline ranges from the loaded SEG-Y file
- Specify minimum and maximum inline/crossline values to define the AOI
- View the number of traces found within the specified AOI
- Proceed directly to the export configuration step

Key features:
- The interface automatically initializes with the full available inline/crossline range
- A two-column layout makes it easy to define min/max values
- The "Full AOI" processing option is the only supported mode for this selection type
- The selected traces and AOI bounds are stored in the session state for downstream processing

### 2. Export Configuration UI (analyze_data_page.py)

Once an AOI is defined, the export configuration interface allows users to:
- Select which seismic attributes to export (e.g., HFC, Normalized Dominant Frequency, WOSS)
- Choose how to group the exported files (by inline or crossline)
- Set the batch size for processing and file organization
- Configure GPU processing parameters (if GPU is available)
- Start the export process

### 3. Export Processing (analyze_data_page.py)

The export processing step handles:
- Batching of traces to manage memory usage and file sizes
- Loading trace data for the current batch
- Calculating attributes using GPU acceleration
- Creating SEG-Y files for each attribute
- Tracking progress with a progress bar
- Error handling and allowing cancellation

Features:
- The export is processed in batches based on the selected grouping (inline or crossline)
- GPU processing is used for efficient calculation of multiple attributes
- Original trace headers are preserved in the exported SEG-Y files
- Each attribute is exported to a separate SEG-Y file with a descriptive name
- Progress tracking shows the current batch number and completion percentage

### 4. Export Download (analyze_data_page.py)

After export processing is complete, the download interface:
- Creates a ZIP file containing all exported SEG-Y files
- Provides a download button for the complete package
- Lists all exported files
- Offers options to start a new export or a completely new analysis

## Data Flow

1. **Step 1 & 2**: User loads a SEG-Y file and configures spectral parameters
2. **Step 3 (Select Mode)**: User selects "By inline/crossline section (AOI)" and defines AOI bounds
3. **Step 4 (Configure Export)**: 
   - User selects attributes for export and configures grouping and batching
   - Backend filters trace indices to only those within the AOI
4. **Step 4.5 (Export Process)**:
   - Traces are processed in batches based on grouping (inline/crossline)
   - For each batch, trace data is loaded and attributes are calculated using GPU
   - SEG-Y files are created for each attribute, preserving original trace headers
5. **Step 4.6 (Download Export)**:
   - All SEG-Y files are packaged into a ZIP file for download
   - User can download the results or start a new analysis

## Technical Implementation Notes

- GPU acceleration is required for AOI export due to the potentially large number of traces
- The `dlogst_spec_descriptor_gpu_2d_chunked` function provides efficient batch processing
- WOSS calculation requires the HFC, Normalized Dominant Frequency, and Magnitude of Voice Slope components
- Export files are automatically organized into batches to keep file sizes manageable
- Original trace headers are preserved to maintain spatial reference information
- All temporary files are created in a system temporary directory and cleaned up after download

## User Workflow

1. Load SEG-Y data in Step 1
2. Configure display parameters in Step 2
3. Select "By inline/crossline section (AOI)" in Step 3
4. Define the AOI bounds and click "Proceed"
5. Select attributes to export and configure batching options in Step 4
6. Click "Start Export Process"
7. Wait for processing to complete, monitoring progress
8. Download the ZIP file with all exported SEG-Y files

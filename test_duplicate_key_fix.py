#!/usr/bin/env python3
"""
Test script to verify that the duplicate key error in select_area_page.py has been fixed.
This script will try to import the module and check for any obvious issues.
"""

import sys
import os

# Add the current directory to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_import():
    """Test if the select_area_page module can be imported without errors."""
    try:
        # Try to import the fixed module
        from pages import select_area_page
        print("✅ SUCCESS: select_area_page.py imported successfully!")
        print("✅ The duplicate key error has been fixed.")
        return True
    except Exception as e:
        print(f"❌ ERROR: Failed to import select_area_page.py: {e}")
        return False

def test_key_uniqueness():
    """Check that the keys used in the file are unique."""
    try:
        with open('pages/select_area_page.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Find all key= patterns
        import re
        key_pattern = r'key="([^"]+)"'
        keys = re.findall(key_pattern, content)
        
        print(f"\n📋 Found {len(keys)} key definitions:")
        for i, key in enumerate(keys, 1):
            print(f"  {i}. {key}")
        
        # Check for duplicates
        unique_keys = set(keys)
        if len(keys) == len(unique_keys):
            print("✅ SUCCESS: All keys are unique!")
            return True
        else:
            duplicates = [key for key in unique_keys if keys.count(key) > 1]
            print(f"❌ ERROR: Found duplicate keys: {duplicates}")
            return False
            
    except Exception as e:
        print(f"❌ ERROR: Failed to check key uniqueness: {e}")
        return False

if __name__ == "__main__":
    print("🔍 Testing duplicate key fix in select_area_page.py...")
    print("=" * 50)
    
    # Test 1: Import test
    import_success = test_import()
    
    # Test 2: Key uniqueness test
    key_success = test_key_uniqueness()
    
    print("\n" + "=" * 50)
    if import_success and key_success:
        print("🎉 ALL TESTS PASSED! The duplicate key error has been successfully fixed.")
    else:
        print("❌ Some tests failed. Please check the issues above.")

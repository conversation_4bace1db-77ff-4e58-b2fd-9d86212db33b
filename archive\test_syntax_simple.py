#!/usr/bin/env python3

import ast
import sys

def check_syntax(filename):
    try:
        with open(filename, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Try to parse the file
        ast.parse(content)
        print(f"✅ {filename}: Syntax is valid")
        return True
        
    except SyntaxError as e:
        print(f"❌ {filename}: Syntax error at line {e.lineno}: {e.msg}")
        if e.text:
            print(f"   Text: {e.text.strip()}")
        return False
        
    except Exception as e:
        print(f"❌ {filename}: Error: {e}")
        return False

if __name__ == "__main__":
    files_to_check = [
        "pages/analyze_data_page.py"
    ]
    
    all_valid = True
    for filename in files_to_check:
        if not check_syntax(filename):
            all_valid = False
    
    if all_valid:
        print("\n🎉 All files have valid syntax!")
    else:
        print("\n💥 Some files have syntax errors!")
        sys.exit(1)
# Plan for Implementing Multi-Well Marker Selection and Processing

This document outlines the plan to modify the WOSS Seismic Analysis Tool to support the selection of multiple well-marker pairs and ensure downstream processing handles these selections correctly. This plan is based on the implementation notes found in `app.py` (lines 199-278) and analysis of `pages/select_area_page.py`, `pages/precompute_qc_page.py`, and `pages/analyze_data_page.py`.

## Key Changes by File:

1.  **`pages/select_area_page.py`:**
    *   **Objective:** Enhance the "Well Markers" mode to correctly process multiple selections and prepare data for the pre-computation step.
    *   **Modifications:**
        *   When the user proceeds from this step (e.g., clicks a "Proceed to Pre-computation & QC" or similar button) after selecting multiple well-marker pairs:
            *   **Convert Labels to Structured Data:** Transform the list of selected string labels (e.g., "WellA - MarkerX") from the `st.multiselect` widget into a structured list of dictionaries. This list will be stored in `st.session_state.selected_well_marker_pairs`. Each dictionary in the list should contain the full data for a well-marker pair (e.g., well name, surface name, X, Y, TWT, original index from `well_df`).
            *   **Update Area Selection Details:** Populate `st.session_state.area_selected_details` with a dictionary containing:
                *   `type`: 'well_markers'
                *   `count`: Number of selected pairs (i.e., `len(st.session_state.selected_well_marker_pairs)`)
                *   `labels`: The list of selected string labels.
            *   **Aggregate Trace Indices:** Generate `st.session_state.selected_indices`. This should be a flat list containing all unique trace indices corresponding to *all* selected well-marker pairs. This involves looking up the nearest trace for each selected pair's coordinates.
            *   **Set Data for Pre-computation:** Assign the structured list of pairs to `st.session_state.selected_data_for_precompute` (i.e., `st.session_state.selected_data_for_precompute = st.session_state.selected_well_marker_pairs`).
            *   **Update Session State Flags:**
                *   Set `st.session_state.area_selected = True`.
                *   Set `st.session_state.area_selected_mode = 'well_markers'`.
            *   **Transition:** Ensure the application correctly transitions to the "precompute_qc" step.

2.  **`pages/precompute_qc_page.py`:**
    *   **Objective:** Ensure the pre-computation and QC step correctly uses the data from multiple selected well-marker pairs.
    *   **Modifications:**
        *   **Display Information:** Update the "Selected Area Information" section to correctly display details based on `st.session_state.area_selected_details` when `area_selected_mode` is 'well_markers'. This should clearly show the selected pairs.
        *   **Trace Processing:** The pre-computation logic (triggered by "Run Pre-computation & QC") will use the `st.session_state.selected_indices` (which now contains trace indices from all selected pairs).
        *   **QC Subset:** The current behavior of processing a subset of `selected_indices` (e.g., the first 10) for QC and storing this subset's results in `st.session_state.precomputed_data_output` is deemed acceptable for this phase. This means `pages/analyze_data_page.py` will only have pre-processed data for these specific QC traces.

3.  **`pages/analyze_data_page.py`:**
    *   **Objective:** Adapt the analysis page to work with potentially multiple well-marker pairs and use pre-computed data where available.
    *   **Modifications:**
        *   **Remove Redundant UI:** Remove the `st.multiselect` widget currently present in this page for re-selecting well-marker pairs, as the definitive selection is now made in `pages/select_area_page.py`.
        *   **Load Trace Data:**
            *   The primary source for identifying which traces to analyze will be `st.session_state.selected_well_marker_pairs` (the structured list of dictionaries from `pages/select_area_page.py`).
            *   Iterate through this list. For each well-marker pair dictionary:
                *   Determine its corresponding `trace_idx` (e.g., by looking up nearest trace based on X, Y coordinates stored in the pair's dictionary).
                *   Check `st.session_state.precomputed_data_output` (from `pages/precompute_qc_page.py`) to see if this `trace_idx` has a pre-processed version.
                *   If a pre-processed trace exists, use it.
                *   If not (i.e., it wasn't part of the QC subset), load the original trace data using `load_trace_sample`.
            *   Compile all loaded traces (either pre-processed or original) into `st.session_state.loaded_trace_data`.
        *   **Descriptor Calculation:** Proceed with descriptor calculation on all traces in `st.session_state.loaded_trace_data`. The rest of the analysis logic should then operate on these potentially mixed (pre-processed and original) traces.

## Mermaid Diagram of Proposed Data Flow:

```mermaid
graph TD
    A[Step 3: Select Area Mode - `select_area_page.py`] -- "Well Markers Mode Selected" --> B{Well Data Loaded?};
    B -- Yes --> C[Display `st.multiselect` for Well-Marker Pairs (labels)];
    B -- No --> BN[Show Warning: Load Well Data];
    C -- User Selects Pairs & Clicks "Proceed to Pre-computation" --> D[Process Selections in `select_area_page.py`];
    D --> E[1. Create `st.session_state.selected_well_marker_pairs` (list of dicts with full pair data)];
    D --> F[2. Create `st.session_state.area_selected_details` (type='well_markers', count, labels)];
    D --> G[3. Create `st.session_state.selected_indices` (flat list of all trace_idx for all selected pairs)];
    D --> H[4. Set `st.session_state.area_selected=True`, `st.session_state.area_selected_mode='well_markers'`];
    D --> I[5. Set `st.session_state.selected_data_for_precompute` = `st.session_state.selected_well_marker_pairs`];
    I -- Transition to next step --> J[Step 3.5: Pre-computation & QC - `precompute_qc_page.py`];
    J --> K{`area_selected` and `selected_data_for_precompute` are OK?};
    K -- Yes --> L[Display Selected Area Info (from `area_selected_details` and `selected_well_marker_pairs`)];
    L --> M[User Sets Pre-computation Parameters & Clicks "Run Pre-computation & QC"];
    M --> N[Load Traces for QC (e.g., first 10 from `selected_indices`)];
    N --> O[Run `run_precomputation` on these QC traces];
    O --> P[Store results in `st.session_state.precomputed_data_output` (contains processed data for QC traces only)];
    P --> Q[Set `st.session_state.precomputation_complete=True`];
    Q --> R[Display QC Results (plots, stats for the QC subset)];
    R -- User Clicks "Next to Analyze Data" --> S[Step 4: Analyze Data - `analyze_data_page.py`];
    S --> T{`precomputation_complete` is True?};
    T -- Yes --> U[Remove redundant well-marker `st.multiselect` UI];
    U --> V[Load Data: Iterate `st.session_state.selected_well_marker_pairs` (structured list)];
    V -- For each pair in list --> W[Determine `trace_idx` for the pair];
    W --> X{Is `trace_idx` in `st.session_state.precomputed_data_output`? (i.e., was it part of QC set)};
    X -- Yes, Found --> Y[Use `processed_trace` from `precomputed_data_output`];
    X -- No, Not Found --> Z[Load original trace data using `load_trace_sample`];
    Y --> AA[Append trace to `loaded_trace_data` list];
    Z --> AA;
    AA -- All selected pairs processed --> AB[User Clicks "Calculate Descriptors"];
    AB --> AC[Calculate spectral descriptors for all traces in `loaded_trace_data`];
    AC --> AD[Store results in `st.session_state.calculated_descriptors`, set `st.session_state.analysis_complete=True`];
    AD --> AE[Display Descriptor Statistics];
    AE -- User Clicks "View Results" --> AF[Transition to Step 5: Export Results / View Results Page];

```

## Session State Variables Involved:

*   `st.session_state.well_df`: DataFrame containing loaded well data. (Input)
*   `st.session_state.header_loader`: Contains seismic header information. (Input)
*   `st.session_state.selection_mode`: Tracks the current area selection mode (e.g., 'By well markers').
*   `st.session_state.selected_well_marker_pairs`: **(Modified/New)** List of dictionaries, each representing a selected well-marker pair with its detailed data. Set in `select_area_page.py`.
*   `st.session_state.area_selected_details`: **(Modified/New)** Dictionary holding type, count, and labels of the selection. Set in `select_area_page.py`.
*   `st.session_state.selected_indices`: **(Modified)** Flat list of all trace indices corresponding to the selection. Aggregated in `select_area_page.py` for all chosen pairs.
*   `st.session_state.area_selected`: Boolean, set to `True` in `select_area_page.py`.
*   `st.session_state.selected_data_for_precompute`: **(Modified)** Set to `st.session_state.selected_well_marker_pairs` in `select_area_page.py`.
*   `st.session_state.precomputation_params`: Parameters for pre-computation. Set in `precompute_qc_page.py`.
*   `st.session_state.precomputed_data_output`: Output of pre-computation (currently for a QC subset). Set in `precompute_qc_page.py`. Used by `analyze_data_page.py`.
*   `st.session_state.precomputation_complete`: Boolean, set in `precompute_qc_page.py`.
*   `st.session_state.qc_results_info`: QC results. Set in `precompute_qc_page.py`.
*   `st.session_state.loaded_trace_data`: List of traces (original or pre-processed) for analysis. Compiled in `analyze_data_page.py`.
*   `st.session_state.calculated_descriptors`: Results of descriptor calculation. Set in `analyze_data_page.py`.
*   `st.session_state.analysis_complete`: Boolean, set in `analyze_data_page.py`.

This plan aims to fulfill the requirements outlined in the `app.py` notes while integrating with the existing application structure.